from django.urls import path, include

from . import views
from .views import *

urlpatterns = [
    path('', views.feed, name='feed'),
    path('jobs/', views.jobs, name='jobs'),
    path('people/', views.people, name='people'),
    path('create/', views.create_job, name='create'),
    path('create/description/', views.job_description, name='description'),
    path('create/publish/', views.job_preview_publish, name='publish'),
    path('profile/', views.profile, name='profile'),
    path('settings/', views.settings, name='settings'),
    path('jobs/details/<str:vacancy_id>/', views.published_job_details_with_id, name='details_with_id'),
    path('application/<str:application_id>/', views.application, name='application'),
    path('signin/', views.signin, name='signin'),
    path('create/preview/', views.job_preview_publish, name='preview'),
    path("initials-avatar/", include('django_initials_avatar.urls')),
    path("insert_comment/", views.insert_comment, name='insert_comment'),
    path("insert_talent_request/", views.insert_talent_request, name='insert_talent_request'),
    path("change_state/", views.change_state, name='change_state'),
    path('settings/permissions/', views.manage_permissions, name='manage_permissions'),
    path('settings/permissions/invite/', views.invite_user, name='invite_user'),
    path('settings/permissions/remove/<int:user_id>/', views.remove_user, name='remove_user'),
    path('settings/preferences/', views.manage_preferences, name='preferences'),
    path('language-test/', views.language_test, name='language_test'),
    path('api/work-schedules/', views.work_schedules, name='work_schedules_api'),
    path('api/work-schedules/<int:schedule_id>/', views.work_schedule_detail, name='work_schedule_detail_api'),
    path('api/office-schedules/', views.office_schedules, name='office_schedules_api'),
    path('api/office-schedules/<int:schedule_id>/', views.office_schedule_detail, name='office_schedule_detail_api'),
    path('api/office-locations/', views.office_locations, name='office_locations_api'),
    path('api/office-locations/<int:location_id>/', views.office_location_detail, name='office_location_detail_api'),
    path('api/departments/', views.departments, name='departments_api'),
    path('api/departments/<int:department_id>/', views.department_detail, name='department_detail_api'),
    path('settings/portals/', views.add_portal, name='portals'),
    path('settings/templates/', views.create_job_template, name='templates'),
    path('api/save-template/', views.save_template, name='save_template'),
    path('api/delete-template/', views.delete_template, name='delete_template'),
    path('api/get-template/', views.get_template, name='get_template'),
    path('api/templates/', views.get_all_templates, name='get_all_templates'),
    path('api/appointments/', views.get_appointments, name='get_appointments'),
    path('api/add_appointment/', views.add_appointment, name='add_appointment'),
    path('api/update_appointment/<int:appointment_id>/', views.update_appointment, name='update_appointment'),
    path('api/delete_appointment/<int:appointment_id>/', views.delete_appointment, name='delete_appointment'),
    path('save-published-job/', views.save_published_job, name='save_published_job'),
    path('update_vacancy_status/', update_vacancy_status, name='update_vacancy_status'),
    path('send_bulk_mails/', views.send_bulk_mails, name='send_bulk_mails'),
    path('send_email/', views.send_email, name='send_email'),
    path('accept-invitation/<str:token>/', views.accept_invitation, name='accept_invitation'),
    path('registration-complete/', views.registration_complete, name='registration_complete'),
    path('change-invitation-status/<int:invitation_id>/<str:status>/', views.change_invitation_status, name='change_invitation_status'),
    path('change-employee-status/<int:user_id>/<str:status>/', views.change_employee_status, name='change_employee_status'),
    path('get_candidates_for_vacancy/', views.get_candidates_for_vacancy, name='get_candidates_for_vacancy'),
    path('change-password/', views.change_password_user, name='change_password'),
    path('change-photo/', views.change_employee_photo, name='change_photo'),
]

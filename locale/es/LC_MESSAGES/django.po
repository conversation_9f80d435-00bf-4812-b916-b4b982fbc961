# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-24 23:42+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: feed/models.py:58
msgid "Active"
msgstr "Activo"

#: feed/models.py:59
msgid "Draft"
msgstr "Borrador"

#: feed/models.py:60
msgid "Closed"
msgstr "Cerrado"

#: feed/models.py:61
msgid "On-Hold"
msgstr "En Espera"

#: feed/models.py:62
msgid "Archived"
msgstr "Archivado"

#: feed/models.py:63
msgid "Reviewing"
msgstr "Revisando"

#: feed/models.py:181
msgid "New"
msgstr "Nuevo"

#: feed/models.py:182
msgid "Review #1"
msgstr "Revisión #1"

#: feed/models.py:183
msgid "Review #2"
msgstr "Revisión #2"

#: feed/models.py:184
msgid "Review #3"
msgstr "Revisión #3"

#: feed/models.py:185
msgid "Review #4"
msgstr "Revisión #4"

#: feed/models.py:186
msgid "Review #5"
msgstr "Revisión #5"

#: feed/models.py:187
msgid "Ready for Decision"
msgstr "Listo para Decisión"

#: feed/models.py:188
msgid "Eliminated"
msgstr "Eliminado"

#: feed/models.py:189
msgid "Offer Made"
msgstr "Oferta Realizada"

#: feed/models.py:190
msgid "Candidate Accepted"
msgstr "Candidato Aceptado"

#: feed/models.py:191 templates/jobs.html:141
msgid "Hired"
msgstr "Contratado"

#: feed/models.py:192
msgid "Candidate Rejected"
msgstr "Candidato Rechazado"

#: feed/models.py:212
msgid "Phone Call"
msgstr "Llamada Telefónica"

#: feed/models.py:213
msgid "Video Call"
msgstr "Videollamada"

#: feed/models.py:214
msgid "Online Interview"
msgstr "Entrevista Online"

#: feed/models.py:215
msgid "Technical Assessment"
msgstr "Evaluación Técnica"

#: feed/models.py:216
msgid "Final Interview"
msgstr "Entrevista Final"

#: feed/models.py:217
msgid "Face to Face Interview"
msgstr "Entrevista Presencial"

#: feed/models.py:218
msgid "Office Visit"
msgstr "Visita a la Oficina"

#: feed/models.py:219
msgid "Other"
msgstr "Otro"

#: feed/views.py:72
#, python-format
msgid "<strong>%(name)s</strong> applied for <strong>%(position)s</strong>"
msgstr ""

#: feed/views.py:100
#, python-format
msgid ""
"<strong>%(name)s</strong> moved to <strong>%(state)s</strong> for "
"<strong>%(position)s</strong>"
msgstr ""

#: feed/views.py:126
#, python-format
msgid "A new vacancy <strong>%(vacancy_title)s</strong> is published"
msgstr ""

#: feed/views.py:149
msgid "New comment on application of"
msgstr ""

#: feed/views.py:151
msgid "New comment on application ID"
msgstr ""

#: feed/views.py:767
msgid "Profile photo changed successfully!"
msgstr ""

#: feed/views.py:772
msgid "Please select a photo."
msgstr ""

#: feed/views.py:1077
#, python-format
msgid "Language changed to %(language)s"
msgstr ""

#: feed/views.py:1081
msgid "Invalid language selection"
msgstr ""

#: feed/views.py:1110 templates/feed.html:16
msgid "Dashboard"
msgstr "Panel de Control"

#: feed/views.py:1233
msgid "Invitation mail sent successfully!"
msgstr ""

#: feed/views.py:1236 feed/views.py:1379
msgid "Failed to send the invitation. Please check the form."
msgstr ""

#: feed/views.py:1268
msgid "Passwords do not match."
msgstr ""

#: feed/views.py:1305
msgid "No employer found to associate with this account."
msgstr ""

#: feed/views.py:1315
msgid "Registration completed successfully! You can now log in."
msgstr ""

#: feed/views.py:1319
#, python-format
msgid "Error creating account: %(error)s"
msgstr ""

#: feed/views.py:1361
msgid "Invitation sent successfully!"
msgstr ""

#: feed/views.py:1389
msgid "User removed successfully!"
msgstr ""

#: feed/views.py:1402
msgid "User status changed successfully!"
msgstr ""

#: feed/views.py:1515
msgid "Talent request sent successfully! Our team will get back to you soon."
msgstr ""

#: feed/views.py:1519
msgid "Invalid request method."
msgstr ""

#: templates/applicant_dev.html:30
msgid "Applied for:"
msgstr ""

#: templates/applicant_dev.html:89
#, fuzzy
#| msgid "Online Interview"
msgid "Schedule Interview"
msgstr "Entrevista Online"

#: templates/applicant_dev.html:99
msgid "Change State"
msgstr ""

#: templates/applicant_dev.html:114
#, fuzzy
#| msgid "Dashboard"
msgid "Dashboard & AI"
msgstr "Panel de Control"

#: templates/applicant_dev.html:123
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate Background"
msgstr "Candidato Aceptado"

#: templates/applicant_dev.html:132
msgid "Resume"
msgstr ""

#: templates/applicant_dev.html:141
msgid "Journey"
msgstr ""

#: templates/applicant_dev.html:150
msgid "Comments"
msgstr ""

#: templates/applicant_dev.html:159
msgid "Emails"
msgstr ""

#: templates/applicant_dev.html:167
msgid "Job Details"
msgstr ""

#: templates/applicant_dev.html:191
msgid "Profile Match Analysis"
msgstr ""

#: templates/applicant_dev.html:199
msgid "Key Highlights"
msgstr ""

#: templates/applicant_dev.html:225
msgid "AI analysis will provide candidate highlights."
msgstr ""

#: templates/applicant_dev.html:235
msgid "Areas for Improvement"
msgstr ""

#: templates/applicant_dev.html:272
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate Summary"
msgstr "Candidato Aceptado"

#: templates/applicant_dev.html:321
msgid ""
"Based on the AI analysis, when the resume is compared to the job "
"requirements,"
msgstr ""

#: templates/applicant_dev.html:323
msgid "This candidate is an"
msgstr ""

#: templates/applicant_dev.html:324
msgid "excellent match"
msgstr ""

#: templates/applicant_dev.html:326 templates/applicant_dev.html:329
#: templates/applicant_dev.html:332
msgid "This candidate is a"
msgstr ""

#: templates/applicant_dev.html:327
msgid "good match"
msgstr ""

#: templates/applicant_dev.html:330
msgid "fair match"
msgstr ""

#: templates/applicant_dev.html:333
msgid "weak match"
msgstr ""

#: templates/applicant_dev.html:336
msgid "Analyze the CV with AI to see match details."
msgstr ""

#: templates/applicant_dev.html:363
msgid "AI Analysis Available"
msgstr ""

#: templates/applicant_dev.html:366
msgid "Leverage AI to analyze this candidate's CV against the job description."
msgstr ""

#: templates/applicant_dev.html:387
msgid "Analyze with CanviderAI"
msgstr ""

#: templates/applicant_dev.html:402
msgid "Analyzing CV..."
msgstr ""

#: templates/applicant_dev.html:403
msgid "This may take a moment"
msgstr ""

#: templates/applicant_dev.html:428 templates/applicant_dev.html:449
msgid "Analysis complete"
msgstr ""

#: templates/applicant_dev.html:460
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate Facts"
msgstr "Candidato Aceptado"

#: templates/applicant_dev.html:467
msgid "Applied Position"
msgstr ""

#: templates/applicant_dev.html:473
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate's Address"
msgstr "Candidato Aceptado"

#: templates/applicant_dev.html:476 templates/people.html:112
#: templates/people.html:119 templates/people.html:126
msgid "Not analyzed"
msgstr ""

#: templates/applicant_dev.html:480 templates/people.html:55
#, fuzzy
#| msgid "Applicants"
msgid "Application Date"
msgstr "Candidatos"

#: templates/applicant_dev.html:486
#, fuzzy
#| msgid "Applicants"
msgid "Application Portal"
msgstr "Candidatos"

#: templates/create_job.html:3
#, fuzzy
#| msgid "Create Job"
msgid "Create Job Position"
msgstr "Crear Empleo"

#: templates/create_job.html:8
msgid "Basic Information"
msgstr ""

#: templates/create_job.html:10
msgid "Role Title"
msgstr ""

#: templates/create_job.html:14
msgid "e.g. Senior Software Engineer"
msgstr ""

#: templates/create_job.html:18
#, fuzzy
#| msgid "Locations"
msgid "Office Location"
msgstr "Ubicaciones"

#: templates/create_job.html:21
msgid "Select office location"
msgstr ""

#: templates/create_job.html:31
msgid "No office locations found. Please"
msgstr ""

#: templates/create_job.html:32
msgid "add office locations"
msgstr ""

#: templates/create_job.html:32 templates/create_job.html:53
#: templates/create_job.html:74
msgid "in your preferences first."
msgstr ""

#: templates/create_job.html:36
msgid "No locations available"
msgstr ""

#: templates/create_job.html:41
#, fuzzy
#| msgid "Work Schedules"
msgid "Work Schedule"
msgstr "Horarios de Trabajo"

#: templates/create_job.html:44 templates/create_job.html:65
msgid "Select an option"
msgstr ""

#: templates/create_job.html:52
msgid "No work schedules found. Please"
msgstr ""

#: templates/create_job.html:53
#, fuzzy
#| msgid "Work Schedules"
msgid "add work schedules"
msgstr "Horarios de Trabajo"

#: templates/create_job.html:57
msgid "No work schedules available"
msgstr ""

#: templates/create_job.html:62
#, fuzzy
#| msgid "Office Schedules"
msgid "Office Schedule"
msgstr "Horarios de Oficina"

#: templates/create_job.html:73
msgid "No office schedules found. Please"
msgstr ""

#: templates/create_job.html:74
#, fuzzy
#| msgid "Office Schedules"
msgid "add office schedules"
msgstr "Horarios de Oficina"

#: templates/create_job.html:78
#, fuzzy
#| msgid "Office Schedules"
msgid "No office schedules available"
msgstr "Horarios de Oficina"

#: templates/create_job.html:86
msgid "Skills Requirements"
msgstr ""

#: templates/create_job.html:88
msgid "Skill"
msgstr ""

#: templates/create_job.html:93
msgid "e.g. JavaScript"
msgstr ""

#: templates/create_job.html:95 templates/create_job.html:169
msgid "Add"
msgstr ""

#: templates/create_job.html:98
msgid "Choose Skills"
msgstr ""

#: templates/create_job.html:112
msgid "Selected Skills"
msgstr ""

#: templates/create_job.html:115
msgid "No skills selected yet"
msgstr ""

#: templates/create_job.html:125
msgid "Salary Details (Optional)"
msgstr ""

#: templates/create_job.html:128
msgid "Minimum Salary"
msgstr ""

#: templates/create_job.html:132
msgid "Enter minimum salary"
msgstr ""

#: templates/create_job.html:136
msgid "Maximum Salary"
msgstr ""

#: templates/create_job.html:140
msgid "Enter maximum salary"
msgstr ""

#: templates/create_job.html:144
#, fuzzy
#| msgid "Current"
msgid "Currency"
msgstr "Actual"

#: templates/create_job.html:146
msgid "Select currency"
msgstr ""

#: templates/create_job.html:162
msgid "Benefits and Highlights (Optional)"
msgstr ""

#: templates/create_job.html:167
msgid "e.g. Yearly Bonuses"
msgstr ""

#: templates/create_job.html:172
msgid "Choose Benefits"
msgstr ""

#: templates/create_job.html:175
msgid "Dental Coverage"
msgstr ""

#: templates/create_job.html:178
msgid "Private Health Coverage"
msgstr ""

#: templates/create_job.html:181
msgid "Gym membership"
msgstr ""

#: templates/create_job.html:184
msgid "Sign-in Bonus"
msgstr ""

#: templates/create_job.html:187
msgid "Relocation Package"
msgstr ""

#: templates/create_job.html:190
msgid "Company Vehicle"
msgstr ""

#: templates/create_job.html:192
msgid "Food Card"
msgstr ""

#: templates/create_job.html:194
msgid "Snacks & Coffee"
msgstr ""

#: templates/create_job.html:197
msgid "Pet Friendly Office"
msgstr ""

#: templates/create_job.html:201
msgid "Selected Benefits & Highlights"
msgstr ""

#: templates/create_job.html:204
msgid "No benefits or highlights selected yet"
msgstr ""

#: templates/create_job.html:211
msgid "Discard"
msgstr ""

#: templates/create_job.html:212
msgid "Next"
msgstr ""

#: templates/feed.html:19 templates/feed.html:45
msgid "Loading..."
msgstr ""

#: templates/feed.html:31
msgid "Calendar"
msgstr "Calendario"

#: templates/feed.html:33
msgid "Day"
msgstr "Día"

#: templates/feed.html:34
msgid "Week"
msgstr "Semana"

#: templates/feed.html:35
msgid "Month"
msgstr "Mes"

#: templates/feed.html:50 templates/jobs.html:92 templates/people.html:58
msgid "Today"
msgstr "Hoy"

#: templates/feed.html:55
msgid "Click on a day with colored dots to view events"
msgstr ""

#: templates/feed.html:60 templates/feed.html:73
#, fuzzy
#| msgid "Month"
msgid "Mon"
msgstr "Mes"

#: templates/feed.html:61 templates/feed.html:74
msgid "Tue"
msgstr ""

#: templates/feed.html:62 templates/feed.html:75
msgid "Wed"
msgstr ""

#: templates/feed.html:63 templates/feed.html:76
msgid "Thu"
msgstr ""

#: templates/feed.html:64 templates/feed.html:77
msgid "Fri"
msgstr ""

#: templates/feed.html:65 templates/feed.html:78
msgid "Sat"
msgstr ""

#: templates/feed.html:66 templates/feed.html:79
msgid "Sun"
msgstr ""

#: templates/feed.html:95
msgid "Activity Feed"
msgstr "Feed de Actividad"

#: templates/feed.html:143
msgid "Hot"
msgstr ""

#: templates/feed.html:143 templates/navbar.html:28
msgid "Jobs"
msgstr "Empleos"

#: templates/feed.html:146
msgid "View All Jobs"
msgstr ""

#: templates/feed.html:162 templates/feed.html:199 templates/jobs.html:128
#: templates/navbar.html:33
msgid "Applicants"
msgstr "Candidatos"

#: templates/feed.html:182
msgid "Monthly Applicant Overview"
msgstr ""

#: templates/feed.html:221
msgid "Events for Date"
msgstr ""

#: templates/feed.html:243
msgid "Add New Event"
msgstr ""

#: templates/feed.html:253
msgid "Create New Event"
msgstr ""

#: templates/feed.html:256
msgid "Event Title"
msgstr ""

#: templates/feed.html:260
msgid "Enter event title"
msgstr ""

#: templates/feed.html:266
msgid "Event Type"
msgstr ""

#: templates/feed.html:272
msgid "Select an event type"
msgstr ""

#: templates/feed.html:280 templates/manage_permissions.html:49
msgid "Recruiters"
msgstr ""

#: templates/feed.html:285
msgid "Select one or many recruiters"
msgstr ""

#: templates/feed.html:338 templates/people.html:25 templates/people.html:81
msgid "Position"
msgstr ""

#: templates/feed.html:347
msgid "Select the relevant position"
msgstr ""

#: templates/feed.html:354
msgid "No vacancies available"
msgstr ""

#: templates/feed.html:360
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate"
msgstr "Candidato Aceptado"

#: templates/feed.html:367
msgid "Pick a Vacancy to see candidates"
msgstr ""

#: templates/feed.html:374
msgid "Start Time"
msgstr ""

#: templates/feed.html:379
msgid "End Time"
msgstr ""

#: templates/feed.html:385
msgid "Meeting Link"
msgstr ""

#: templates/feed.html:389
msgid "Enter meeting link"
msgstr ""

#: templates/feed.html:403
msgid "Inform invitees by E-mail"
msgstr ""

#: templates/feed.html:408
msgid "Color"
msgstr ""

#: templates/feed.html:410
msgid "Blue"
msgstr ""

#: templates/feed.html:411
msgid "Light Blue"
msgstr ""

#: templates/feed.html:412
msgid "Purple"
msgstr ""

#: templates/feed.html:413
msgid "Pink"
msgstr ""

#: templates/feed.html:419
msgid "Cancel"
msgstr ""

#: templates/feed.html:422
msgid "Save Event"
msgstr ""

#: templates/job_preferences.html:11 templates/job_preferences.html:17
#: templates/settings.html:20
msgid "Preferences"
msgstr "Preferencias"

#: templates/job_preferences.html:12
msgid "Configure standard options to streamline your job creation process"
msgstr ""
"Configure opciones estándar para agilizar su proceso de creación de empleos"

#: templates/job_preferences.html:15 templates/manage_permissions.html:15
#: templates/navbar.html:137 templates/settings.html:9
msgid "Settings"
msgstr "Configuración"

#: templates/job_preferences.html:26
msgid "Work Schedules"
msgstr "Horarios de Trabajo"

#: templates/job_preferences.html:30
msgid "Office Schedules"
msgstr "Horarios de Oficina"

#: templates/job_preferences.html:34
msgid "Locations"
msgstr "Ubicaciones"

#: templates/job_preferences.html:38
msgid "Departments"
msgstr "Departamentos"

#: templates/job_preferences.html:42
msgid "Language"
msgstr "Idioma"

#: templates/job_preferences.html:183
msgid "Language Settings"
msgstr "Configuración de Idioma"

#: templates/job_preferences.html:184
msgid "Choose your preferred language for the application interface"
msgstr "Elija su idioma preferido para la interfaz de la aplicación"

#: templates/job_preferences.html:189
msgid "Interface Language"
msgstr "Idioma de la Interfaz"

#: templates/job_preferences.html:190
msgid "Select the language you want to use for the application interface"
msgstr "Seleccione el idioma que desea usar para la interfaz de la aplicación"

#: templates/job_preferences.html:210
msgid "Current"
msgstr "Actual"

#: templates/job_preferences.html:223
msgid "Note:"
msgstr "Nota:"

#: templates/job_preferences.html:223
msgid ""
"Changing the language will refresh the page to apply the new language "
"settings."
msgstr ""
"Cambiar el idioma actualizará la página para aplicar la nueva configuración "
"de idioma."

#: templates/jobs.html:8
msgid "Job Listings"
msgstr ""

#: templates/jobs.html:18
#, fuzzy
#| msgid "Active"
msgid "Active Jobs"
msgstr "Activo"

#: templates/jobs.html:28
#, fuzzy
#| msgid "Applicants"
msgid "Total Applicants"
msgstr "Candidatos"

#: templates/jobs.html:38
#, fuzzy
#| msgid "Archived"
msgid "Archived Jobs"
msgstr "Archivado"

#: templates/jobs.html:48
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidates Hired"
msgstr "Candidato Aceptado"

#: templates/jobs.html:59
#, fuzzy
#| msgid "Departments"
msgid "Department"
msgstr "Departamentos"

#: templates/jobs.html:61
#, fuzzy
#| msgid "Departments"
msgid "All Departments"
msgstr "Departamentos"

#: templates/jobs.html:69 templates/people.html:35 templates/people.html:82
msgid "Status"
msgstr ""

#: templates/jobs.html:71 templates/people.html:37
msgid "All Statuses"
msgstr ""

#: templates/jobs.html:79 templates/people.html:45 templates/people.html:83
#, fuzzy
#| msgid "Locations"
msgid "Location"
msgstr "Ubicaciones"

#: templates/jobs.html:81 templates/people.html:47
#, fuzzy
#| msgid "Locations"
msgid "All Locations"
msgstr "Ubicaciones"

#: templates/jobs.html:89
msgid "Posted Date"
msgstr ""

#: templates/jobs.html:91
msgid "All Time"
msgstr ""

#: templates/jobs.html:93 templates/people.html:59
#, fuzzy
#| msgid "Week"
msgid "This Week"
msgstr "Semana"

#: templates/jobs.html:94 templates/people.html:60
#, fuzzy
#| msgid "Month"
msgid "This Month"
msgstr "Mes"

#: templates/jobs.html:95
#, fuzzy
#| msgid "Month"
msgid "Last Month"
msgstr "Mes"

#: templates/jobs.html:106 templates/people.html:70
msgid "Clear all filters"
msgstr ""

#: templates/jobs.html:134
#, fuzzy
#| msgid "Final Interview"
msgid "Interviews"
msgstr "Entrevista Final"

#: templates/jobs.html:145
msgid "Days Open"
msgstr ""

#: templates/jobs.html:151
#, fuzzy
#| msgid "Closed"
msgid "Closed on:"
msgstr "Cerrado"

#: templates/jobs.html:151
msgid "Posted on:"
msgstr ""

#: templates/jobs.html:152
msgid "View Details"
msgstr ""

#: templates/jobs.html:162 templates/people.html:151
msgid "Showing"
msgstr ""

#: templates/jobs.html:162 templates/people.html:151
msgid "of"
msgstr ""

#: templates/jobs.html:162
msgid "jobs"
msgstr ""

#: templates/manage_permissions.html:11
msgid "Team & Invitations"
msgstr ""

#: templates/manage_permissions.html:12
msgid "Manage your recruitment team and invite new members"
msgstr ""

#: templates/manage_permissions.html:17 templates/manage_permissions.html:39
#: templates/settings.html:62
#, fuzzy
#| msgid "Locations"
msgid "Invitations"
msgstr "Ubicaciones"

#: templates/manage_permissions.html:29
msgid "Team Members"
msgstr ""

#: templates/manage_permissions.html:59
msgid "Administrators"
msgstr ""

#: templates/navbar.html:23
msgid "Feed"
msgstr "Inicio"

#: templates/navbar.html:41
msgid "Create Job"
msgstr "Crear Empleo"

#: templates/navbar.html:121
msgid "Guest User"
msgstr ""

#: templates/navbar.html:128
msgid "Not logged in"
msgstr ""

#: templates/navbar.html:134
msgid "Profile"
msgstr "Perfil"

#: templates/navbar.html:141
msgid "Logout"
msgstr "Cerrar Sesión"

#: templates/people.html:8
msgid "Applicant Tracking"
msgstr "Seguimiento de Candidatos"

#: templates/people.html:12
msgid "Search applicants..."
msgstr "Buscar candidatos..."

#: templates/people.html:27
msgid "All Positions"
msgstr ""

#: templates/people.html:57
msgid "All Dates"
msgstr ""

#: templates/people.html:80
msgid "Name"
msgstr ""

#: templates/people.html:84
msgid "Experience (Years)"
msgstr ""

#: templates/people.html:85
msgid "Score"
msgstr ""

#: templates/people.html:86
#, fuzzy
#| msgid "Applicants"
msgid "Applied On"
msgstr "Candidatos"

#: templates/people.html:87
#, fuzzy
#| msgid "Active"
msgid "Action"
msgstr "Activo"

#: templates/people.html:134
#, fuzzy
#| msgid "Applicants"
msgid "View Application"
msgstr "Candidatos"

#: templates/people.html:141
msgid "No applicants found matching the current filters."
msgstr ""

#: templates/people.html:151
#, fuzzy
#| msgid "Applicants"
msgid "applicants"
msgstr "Candidatos"

#: templates/people.html:204
msgid "Show"
msgstr ""

#: templates/people.html:211
msgid "per page"
msgstr ""

#: templates/register.html:11
msgid "Accept Invitation"
msgstr ""

#: templates/register.html:15
msgid "This invitation has expired or already been used."
msgstr ""

#: templates/register.html:18
msgid "Hello"
msgstr ""

#: templates/register.html:18
msgid "you've been invited to join"
msgstr ""

#: templates/register.html:18
msgid "as a"
msgstr ""

#: templates/register.html:24 templates/signin.html:15
msgid "Email"
msgstr ""

#: templates/register.html:29
#, fuzzy
#| msgid "Create Job"
msgid "Create Password"
msgstr "Crear Empleo"

#: templates/register.html:34
msgid "Confirm Password"
msgstr ""

#: templates/register.html:39
msgid "Complete Registration"
msgstr ""

#: templates/register.html:58
msgid "Passwords do not match"
msgstr ""

#: templates/settings.html:10
msgid "Configure your recruitment workflow and manage your ATS settings"
msgstr ""
"Configure su flujo de trabajo de reclutamiento y administre la configuración "
"de su ATS"

#: templates/settings.html:21
msgid ""
"Configure default options for job creation including work schedules, office "
"locations, and role titles."
msgstr ""

#: templates/settings.html:23
msgid "Define company work schedules"
msgstr ""

#: templates/settings.html:24
msgid "Set up office locations"
msgstr ""

#: templates/settings.html:25
msgid "Standardize role titles"
msgstr ""

#: templates/settings.html:26
msgid "Configure office schedule options"
msgstr ""

#: templates/settings.html:29
#, fuzzy
#| msgid "Preferences"
msgid "Manage Preferences"
msgstr "Preferencias"

#: templates/settings.html:41
msgid "Templates"
msgstr ""

#: templates/settings.html:42
#, fuzzy
#| msgid "Configure standard options to streamline your job creation process"
msgid ""
"Create, edit, and manage job description templates to streamline your job "
"posting process."
msgstr ""
"Configure opciones estándar para agilizar su proceso de creación de empleos"

#: templates/settings.html:44
msgid "Build reusable job templates"
msgstr ""

#: templates/settings.html:45
msgid "Save time on repetitive descriptions"
msgstr ""

#: templates/settings.html:46
msgid "Maintain consistent job postings"
msgstr ""

#: templates/settings.html:47
msgid "Organize templates by department"
msgstr ""

#: templates/settings.html:50
msgid "Manage Templates"
msgstr ""

#: templates/settings.html:63
msgid ""
"Invite team members to collaborate on your recruitment process and manage "
"user access."
msgstr ""

#: templates/settings.html:65
msgid "Add colleagues to your ATS"
msgstr ""

#: templates/settings.html:66
msgid "Set user permissions"
msgstr ""

#: templates/settings.html:67
msgid "Track invitation status"
msgstr ""

#: templates/settings.html:68
msgid "Manage team collaboration"
msgstr ""

#: templates/settings.html:71
#, fuzzy
#| msgid "Language Settings"
msgid "Manage Invitations"
msgstr "Configuración de Idioma"

#: templates/settings.html:84
msgid "Job Portals"
msgstr ""

#: templates/settings.html:85
msgid ""
"Configure connections to external job boards and manage API credentials for "
"job publishing."
msgstr ""

#: templates/settings.html:87
msgid "Connect to major job boards"
msgstr ""

#: templates/settings.html:88
msgid "Manage API tokens securely"
msgstr ""

#: templates/settings.html:89
msgid "Customize portal preferences"
msgstr ""

#: templates/settings.html:90
msgid "Track portal integration status"
msgstr ""

#: templates/settings.html:93
msgid "(Coming Soon!)"
msgstr ""

#: templates/settings.html:103
msgid "Need Help?"
msgstr ""

#: templates/settings.html:104
msgid ""
"Our support team is ready to assist you with any questions about configuring "
"your ATS."
msgstr ""

#: templates/settings.html:105
msgid "Contact Support"
msgstr ""

#: templates/signin.html:16
msgid "<EMAIL>"
msgstr ""

#: templates/signin.html:20
msgid "Password"
msgstr ""

#: templates/signin.html:48
msgid "Signin"
msgstr ""

#, fuzzy
#~| msgid "Jobs"
#~ msgid "Hot Jobs"
#~ msgstr "Empleos"

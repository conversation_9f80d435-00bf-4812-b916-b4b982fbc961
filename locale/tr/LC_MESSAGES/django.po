# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-24 23:42+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: feed/models.py:58
#, fuzzy
#| msgid "Activity Feed"
msgid "Active"
msgstr "Aktif"

#: feed/models.py:59
msgid "Draft"
msgstr "Taslak"

#: feed/models.py:60
msgid "Closed"
msgstr "Kapalı"

#: feed/models.py:61
msgid "On-Hold"
msgstr "Beklemede"

#: feed/models.py:62
msgid "Archived"
msgstr "Arşivlendi"

#: feed/models.py:63
msgid "Reviewing"
msgstr "Gözden geçiriliyor"

#: feed/models.py:181
msgid "New"
msgstr "Yeni"

#: feed/models.py:182
msgid "Review #1"
msgstr "Görüşme #1"

#: feed/models.py:183
msgid "Review #2"
msgstr "Görüşme #2"

#: feed/models.py:184
msgid "Review #3"
msgstr "Görüşme #3"

#: feed/models.py:185
msgid "Review #4"
msgstr "Görüşme #4"

#: feed/models.py:186
msgid "Review #5"
msgstr "Görüşme #5"

#: feed/models.py:187
msgid "Ready for Decision"
msgstr "Karar için hazır"

#: feed/models.py:188
msgid "Eliminated"
msgstr "Elimine edildi"

#: feed/models.py:189
msgid "Offer Made"
msgstr "Teklif verildi"

#: feed/models.py:190
msgid "Candidate Accepted"
msgstr "Aday kabul etti"

#: feed/models.py:191 templates/jobs.html:141
msgid "Hired"
msgstr "İşe alındı"

#: feed/models.py:192
msgid "Candidate Rejected"
msgstr "Aday reddetti"

#: feed/models.py:212
msgid "Phone Call"
msgstr "Telefon görüşmesi"

#: feed/models.py:213
msgid "Video Call"
msgstr "Video görüşmesi"

#: feed/models.py:214
msgid "Online Interview"
msgstr "Online görüşmesi"

#: feed/models.py:215
msgid "Technical Assessment"
msgstr "Teknik değerlendirme"

#: feed/models.py:216
msgid "Final Interview"
msgstr "Son görüşme"

#: feed/models.py:217
msgid "Face to Face Interview"
msgstr "Yüz yüze görüşme"

#: feed/models.py:218
msgid "Office Visit"
msgstr "Ofis ziyareti"

#: feed/models.py:219
msgid "Other"
msgstr "Diğer"

#: feed/views.py:72
#, python-format
msgid "<strong>%(name)s</strong> applied for <strong>%(position)s</strong>"
msgstr ""
"<strong>%(name)s</strong>, <strong>%(position)s</strong> pozisyonuna başvurdu"

#: feed/views.py:100
#, python-format
msgid ""
"<strong>%(name)s</strong> moved to <strong>%(state)s</strong> for "
"<strong>%(position)s</strong>"
msgstr ""
" <strong>%(name)s</strong>, başvurduğu <strong>%(position)s</strong> "
"pozisyonu için <strong>%(state)s</strong> durumuna taşındı "

#: feed/views.py:126
#, python-format
msgid "A new vacancy <strong>%(vacancy_title)s</strong> is published"
msgstr "Yeni iş ilanı: <strong>%(vacancy_title)s</strong> yayınlandı"

#: feed/views.py:149
msgid "New comment on application of"
msgstr "Yeni yorum eklendi:"

#: feed/views.py:151
msgid "New comment on application ID"
msgstr "Yeni yorum eklendi:"

#: feed/views.py:767
msgid "Profile photo changed successfully!"
msgstr "Profil fotoğrafı başarıyla değiştirildi!"

#: feed/views.py:772
msgid "Please select a photo."
msgstr "Lütfen bir fotoğraf seçin."

#: feed/views.py:1077
#, python-format
msgid "Language changed to %(language)s"
msgstr "Dil %(language)s olarak değiştirildi"

#: feed/views.py:1081
msgid "Invalid language selection"
msgstr "Geçersiz dil seçimi"

#: feed/views.py:1110 templates/feed.html:16
msgid "Dashboard"
msgstr "Panel"

#: feed/views.py:1233
msgid "Invitation mail sent successfully!"
msgstr "Davet e-postası başarıyla gönderildi!"

#: feed/views.py:1236 feed/views.py:1379
msgid "Failed to send the invitation. Please check the form."
msgstr "Davet gönderilemedi. Lütfen formu kontrol edin."

#: feed/views.py:1268
msgid "Passwords do not match."
msgstr "Şifreler eşleşmiyor."

#: feed/views.py:1305
msgid "No employer found to associate with this account."
msgstr "Bu hesapla ilişkilendirilecek işveren bulunamadı."

#: feed/views.py:1315
msgid "Registration completed successfully! You can now log in."
msgstr "Kayıt başarıyla tamamlandı! Artık giriş yapabilirsiniz."

#: feed/views.py:1319
#, python-format
msgid "Error creating account: %(error)s"
msgstr "Hesap oluşturulurken hata: %(error)s"

#: feed/views.py:1361
msgid "Invitation sent successfully!"
msgstr "Davet başarıyla gönderildi!"

#: feed/views.py:1389
msgid "User removed successfully!"
msgstr "Kullanıcı başarıyla kaldırıldı!"

#: feed/views.py:1402
msgid "User status changed successfully!"
msgstr "Kullanıcı durumu başarıyla değiştirildi!"

#: feed/views.py:1515
msgid "Talent request sent successfully! Our team will get back to you soon."
msgstr "Yetenek talebi başarıyla gönderildi! Ekibimiz en kısa sürede size dönüş yapacak."

#: feed/views.py:1519
msgid "Invalid request method."
msgstr "Geçersiz istek yöntemi."

#: templates/applicant_dev.html:30
msgid "Applied for:"
msgstr "Başvurduğu pozisyon:"

#: templates/applicant_dev.html:89
msgid "Schedule Interview"
msgstr "Görüşme Planla"

#: templates/applicant_dev.html:99
msgid "Change State"
msgstr "Durumu Değiştir"

#: templates/applicant_dev.html:114
msgid "Dashboard & AI"
msgstr "Panel & AI"

#: templates/applicant_dev.html:123
msgid "Candidate Background"
msgstr "Aday Geçmişi"

#: templates/applicant_dev.html:132
msgid "Resume"
msgstr "Özgeçmiş"

#: templates/applicant_dev.html:141
msgid "Journey"
msgstr "Süreç"

#: templates/applicant_dev.html:150
msgid "Comments"
msgstr "Yorumlar"

#: templates/applicant_dev.html:159
msgid "Emails"
msgstr "E-postalar"

#: templates/applicant_dev.html:167
msgid "Job Details"
msgstr "İş Detayları"

#: templates/applicant_dev.html:191
msgid "Profile Match Analysis"
msgstr "Profil Uyum Analizi"

#: templates/applicant_dev.html:199
msgid "Key Highlights"
msgstr "Öne Çıkan Özellikler"

#: templates/applicant_dev.html:225
msgid "AI analysis will provide candidate highlights."
msgstr "AI analizi aday öne çıkan özelliklerini sağlayacak."

#: templates/applicant_dev.html:235
msgid "Areas for Improvement"
msgstr "Gelişim Alanları"

#: templates/applicant_dev.html:272
msgid "Candidate Summary"
msgstr "Aday Özeti"

#: templates/applicant_dev.html:321
msgid ""
"Based on the AI analysis, when the resume is compared to the job "
"requirements,"
msgstr ""

#: templates/applicant_dev.html:323
msgid "This candidate is an"
msgstr ""

#: templates/applicant_dev.html:324
msgid "excellent match"
msgstr ""

#: templates/applicant_dev.html:326 templates/applicant_dev.html:329
#: templates/applicant_dev.html:332
msgid "This candidate is a"
msgstr ""

#: templates/applicant_dev.html:327
msgid "good match"
msgstr ""

#: templates/applicant_dev.html:330
msgid "fair match"
msgstr ""

#: templates/applicant_dev.html:333
msgid "weak match"
msgstr ""

#: templates/applicant_dev.html:336
msgid "Analyze the CV with AI to see match details."
msgstr ""

#: templates/applicant_dev.html:363
#, fuzzy
#| msgid "No vacancies available"
msgid "AI Analysis Available"
msgstr "Su anda pozisyon yok"

#: templates/applicant_dev.html:366
msgid "Leverage AI to analyze this candidate's CV against the job description."
msgstr ""

#: templates/applicant_dev.html:387
msgid "Analyze with CanviderAI"
msgstr ""

#: templates/applicant_dev.html:402
msgid "Analyzing CV..."
msgstr ""

#: templates/applicant_dev.html:403
msgid "This may take a moment"
msgstr ""

#: templates/applicant_dev.html:428 templates/applicant_dev.html:449
msgid "Analysis complete"
msgstr ""

#: templates/applicant_dev.html:460
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate Facts"
msgstr "Aday kabul etti"

#: templates/applicant_dev.html:467
#, fuzzy
#| msgid "Position"
msgid "Applied Position"
msgstr "Pozisyon"

#: templates/applicant_dev.html:473
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate's Address"
msgstr "Aday kabul etti"

#: templates/applicant_dev.html:476 templates/people.html:112
#: templates/people.html:119 templates/people.html:126
msgid "Not analyzed"
msgstr ""

#: templates/applicant_dev.html:480 templates/people.html:55
#, fuzzy
#| msgid "Applicants"
msgid "Application Date"
msgstr "Adaylar"

#: templates/applicant_dev.html:486
#, fuzzy
#| msgid "Applicants"
msgid "Application Portal"
msgstr "Adaylar"

#: templates/create_job.html:3
#, fuzzy
#| msgid "Create Job"
msgid "Create Job Position"
msgstr "İlan Oluştur"

#: templates/create_job.html:8
msgid "Basic Information"
msgstr ""

#: templates/create_job.html:10
#, fuzzy
#| msgid "Events for Date"
msgid "Role Title"
msgstr "Bu Tarihteki Etkinlikler"

#: templates/create_job.html:14
msgid "e.g. Senior Software Engineer"
msgstr ""

#: templates/create_job.html:18
#, fuzzy
#| msgid "Locations"
msgid "Office Location"
msgstr "Konumlar"

#: templates/create_job.html:21
#, fuzzy
#| msgid "Select the relevant position"
msgid "Select office location"
msgstr "İlgili pozisyonu seçin"

#: templates/create_job.html:31
msgid "No office locations found. Please"
msgstr ""

#: templates/create_job.html:32
msgid "add office locations"
msgstr ""

#: templates/create_job.html:32 templates/create_job.html:53
#: templates/create_job.html:74
msgid "in your preferences first."
msgstr ""

#: templates/create_job.html:36
#, fuzzy
#| msgid "No vacancies available"
msgid "No locations available"
msgstr "Su anda pozisyon yok"

#: templates/create_job.html:41
#, fuzzy
#| msgid "Work Schedules"
msgid "Work Schedule"
msgstr "Çalışma Zamanları"

#: templates/create_job.html:44 templates/create_job.html:65
#, fuzzy
#| msgid "Select the relevant position"
msgid "Select an option"
msgstr "İlgili pozisyonu seçin"

#: templates/create_job.html:52
msgid "No work schedules found. Please"
msgstr ""

#: templates/create_job.html:53
#, fuzzy
#| msgid "Work Schedules"
msgid "add work schedules"
msgstr "Çalışma Zamanları"

#: templates/create_job.html:57
#, fuzzy
#| msgid "No vacancies available"
msgid "No work schedules available"
msgstr "Su anda pozisyon yok"

#: templates/create_job.html:62
#, fuzzy
#| msgid "Office Schedules"
msgid "Office Schedule"
msgstr "Ofis Ziyaret Sıklığı"

#: templates/create_job.html:73
msgid "No office schedules found. Please"
msgstr ""

#: templates/create_job.html:74
#, fuzzy
#| msgid "Office Schedules"
msgid "add office schedules"
msgstr "Ofis Ziyaret Sıklığı"

#: templates/create_job.html:78
#, fuzzy
#| msgid "No vacancies available"
msgid "No office schedules available"
msgstr "Su anda pozisyon yok"

#: templates/create_job.html:86
msgid "Skills Requirements"
msgstr ""

#: templates/create_job.html:88
msgid "Skill"
msgstr ""

#: templates/create_job.html:93
msgid "e.g. JavaScript"
msgstr ""

#: templates/create_job.html:95 templates/create_job.html:169
msgid "Add"
msgstr ""

#: templates/create_job.html:98
msgid "Choose Skills"
msgstr ""

#: templates/create_job.html:112
msgid "Selected Skills"
msgstr ""

#: templates/create_job.html:115
msgid "No skills selected yet"
msgstr ""

#: templates/create_job.html:125
msgid "Salary Details (Optional)"
msgstr ""

#: templates/create_job.html:128
msgid "Minimum Salary"
msgstr ""

#: templates/create_job.html:132
msgid "Enter minimum salary"
msgstr ""

#: templates/create_job.html:136
msgid "Maximum Salary"
msgstr ""

#: templates/create_job.html:140
msgid "Enter maximum salary"
msgstr ""

#: templates/create_job.html:144
#, fuzzy
#| msgid "Current"
msgid "Currency"
msgstr "Mevcut"

#: templates/create_job.html:146
msgid "Select currency"
msgstr ""

#: templates/create_job.html:162
msgid "Benefits and Highlights (Optional)"
msgstr ""

#: templates/create_job.html:167
msgid "e.g. Yearly Bonuses"
msgstr ""

#: templates/create_job.html:172
msgid "Choose Benefits"
msgstr ""

#: templates/create_job.html:175
msgid "Dental Coverage"
msgstr ""

#: templates/create_job.html:178
msgid "Private Health Coverage"
msgstr ""

#: templates/create_job.html:181
msgid "Gym membership"
msgstr ""

#: templates/create_job.html:184
msgid "Sign-in Bonus"
msgstr ""

#: templates/create_job.html:187
msgid "Relocation Package"
msgstr ""

#: templates/create_job.html:190
msgid "Company Vehicle"
msgstr ""

#: templates/create_job.html:192
msgid "Food Card"
msgstr ""

#: templates/create_job.html:194
msgid "Snacks & Coffee"
msgstr ""

#: templates/create_job.html:197
msgid "Pet Friendly Office"
msgstr ""

#: templates/create_job.html:201
msgid "Selected Benefits & Highlights"
msgstr ""

#: templates/create_job.html:204
msgid "No benefits or highlights selected yet"
msgstr ""

#: templates/create_job.html:211
msgid "Discard"
msgstr ""

#: templates/create_job.html:212
msgid "Next"
msgstr ""

#: templates/feed.html:19 templates/feed.html:45
msgid "Loading..."
msgstr "Yükleniyor..."

#: templates/feed.html:31
msgid "Calendar"
msgstr "Takvim"

#: templates/feed.html:33
msgid "Day"
msgstr "Gün"

#: templates/feed.html:34
msgid "Week"
msgstr "Hafta"

#: templates/feed.html:35
msgid "Month"
msgstr "Ay"

#: templates/feed.html:50 templates/jobs.html:92 templates/people.html:58
msgid "Today"
msgstr "Bugün"

#: templates/feed.html:55
msgid "Click on a day with colored dots to view events"
msgstr "Renkli noktalarla tıklayarak etkinlikleri görüntüleyin"

#: templates/feed.html:60 templates/feed.html:73
#, fuzzy
#| msgid "Month"
msgid "Mon"
msgstr "Pzt"

#: templates/feed.html:61 templates/feed.html:74
msgid "Tue"
msgstr "Sal"

#: templates/feed.html:62 templates/feed.html:75
msgid "Wed"
msgstr "Çar"

#: templates/feed.html:63 templates/feed.html:76
msgid "Thu"
msgstr "Per"

#: templates/feed.html:64 templates/feed.html:77
msgid "Fri"
msgstr "Cum"

#: templates/feed.html:65 templates/feed.html:78
msgid "Sat"
msgstr "Cmt"

#: templates/feed.html:66 templates/feed.html:79
msgid "Sun"
msgstr "Paz"

#: templates/feed.html:95
msgid "Activity Feed"
msgstr "Güncel Gelişmeler"

#: templates/feed.html:143
msgid "Hot"
msgstr "Popüler"

#: templates/feed.html:143 templates/navbar.html:28
msgid "Jobs"
msgstr "İlanlar"

#: templates/feed.html:146
msgid "View All Jobs"
msgstr "Tüm İlanları Görüntüle"

#: templates/feed.html:162 templates/feed.html:199 templates/jobs.html:128
#: templates/navbar.html:33
msgid "Applicants"
msgstr "Adaylar"

#: templates/feed.html:182
msgid "Monthly Applicant Overview"
msgstr "Aylık Aday Genel Bakış"

#: templates/feed.html:221
msgid "Events for Date"
msgstr "Bu Tarihteki Etkinlikler"

#: templates/feed.html:243
msgid "Add New Event"
msgstr "Yeni Etkinlik Ekle"

#: templates/feed.html:253
msgid "Create New Event"
msgstr "Yeni Etkinlik Oluştur"

#: templates/feed.html:256
#, fuzzy
#| msgid "Events for Date"
msgid "Event Title"
msgstr "Bu Tarihteki Etkinlikler"

#: templates/feed.html:260
#, fuzzy
#| msgid "Events for Date"
msgid "Enter event title"
msgstr "Bu Tarihteki Etkinlikler"

#: templates/feed.html:266
msgid "Event Type"
msgstr "Etkinlik Türü"

#: templates/feed.html:272
msgid "Select an event type"
msgstr "Bir etkinlik türü seçin"

#: templates/feed.html:280 templates/manage_permissions.html:49
msgid "Recruiters"
msgstr "İşe Alım Uzmanları"

#: templates/feed.html:285
msgid "Select one or many recruiters"
msgstr "Bir veya birden fazla işe alım uzmanı seçin"

#: templates/feed.html:338 templates/people.html:25 templates/people.html:81
msgid "Position"
msgstr "Pozisyon"

#: templates/feed.html:347
msgid "Select the relevant position"
msgstr "İlgili pozisyonu seçin"

#: templates/feed.html:354
msgid "No vacancies available"
msgstr "Su anda pozisyon yok"

#: templates/feed.html:360
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidate"
msgstr "Aday kabul etti"

#: templates/feed.html:367
msgid "Pick a Vacancy to see candidates"
msgstr "Adayları görmek için bir pozisyon seçin"

#: templates/feed.html:374
msgid "Start Time"
msgstr "Başlangıç Zamanı"

#: templates/feed.html:379
msgid "End Time"
msgstr "Bitiş Zamanı"

#: templates/feed.html:385
msgid "Meeting Link"
msgstr "Toplantı Linki"

#: templates/feed.html:389
#, fuzzy
#| msgid "Meeting Link"
msgid "Enter meeting link"
msgstr "Toplantı Linki"

#: templates/feed.html:403
msgid "Inform invitees by E-mail"
msgstr "Davetlileri E-posta ile bilgilendir"

#: templates/feed.html:408
msgid "Color"
msgstr "Renk"

#: templates/feed.html:410
msgid "Blue"
msgstr "Mavi"

#: templates/feed.html:411
msgid "Light Blue"
msgstr "Açık Mavi"

#: templates/feed.html:412
msgid "Purple"
msgstr "Mor"

#: templates/feed.html:413
msgid "Pink"
msgstr "Pembe"

#: templates/feed.html:419
msgid "Cancel"
msgstr "İptal"

#: templates/feed.html:422
#, fuzzy
#| msgid "Create New Event"
msgid "Save Event"
msgstr "Etkinliği Kaydet"

#: templates/job_preferences.html:11 templates/job_preferences.html:17
#: templates/settings.html:20
msgid "Preferences"
msgstr "Tercihler"

#: templates/job_preferences.html:12
msgid "Configure standard options to streamline your job creation process"
msgstr ""
"İş oluşturma sürecinizi kolaylaştırmak için standart seçenekleri yapılandırın"

#: templates/job_preferences.html:15 templates/manage_permissions.html:15
#: templates/navbar.html:137 templates/settings.html:9
msgid "Settings"
msgstr "Ayarlar"

#: templates/job_preferences.html:26
msgid "Work Schedules"
msgstr "Çalışma Zamanları"

#: templates/job_preferences.html:30
msgid "Office Schedules"
msgstr "Ofis Ziyaret Sıklığı"

#: templates/job_preferences.html:34
msgid "Locations"
msgstr "Konumlar"

#: templates/job_preferences.html:38
msgid "Departments"
msgstr "Departmanlar"

#: templates/job_preferences.html:42
msgid "Language"
msgstr "Dil"

#: templates/job_preferences.html:183
msgid "Language Settings"
msgstr "Dil Ayarları"

#: templates/job_preferences.html:184
msgid "Choose your preferred language for the application interface"
msgstr "Uygulama arayüzü için tercih ettiğiniz dili seçin"

#: templates/job_preferences.html:189
msgid "Interface Language"
msgstr "Arayüz Dili"

#: templates/job_preferences.html:190
msgid "Select the language you want to use for the application interface"
msgstr "Uygulama arayüzü için kullanmak istediğiniz dili seçin"

#: templates/job_preferences.html:210
msgid "Current"
msgstr "Mevcut"

#: templates/job_preferences.html:223
msgid "Note:"
msgstr "Not:"

#: templates/job_preferences.html:223
msgid ""
"Changing the language will refresh the page to apply the new language "
"settings."
msgstr ""
"Dili değiştirmek, yeni dil ayarlarını uygulamak için sayfayı yenileyecektir."

#: templates/jobs.html:8
msgid "Job Listings"
msgstr ""

#: templates/jobs.html:18
#, fuzzy
#| msgid "Activity Feed"
msgid "Active Jobs"
msgstr "Aktif"

#: templates/jobs.html:28
#, fuzzy
#| msgid "Applicants"
msgid "Total Applicants"
msgstr "Adaylar"

#: templates/jobs.html:38
#, fuzzy
#| msgid "Archived"
msgid "Archived Jobs"
msgstr "Arşivlendi"

#: templates/jobs.html:48
#, fuzzy
#| msgid "Candidate Accepted"
msgid "Candidates Hired"
msgstr "Aday kabul etti"

#: templates/jobs.html:59
#, fuzzy
#| msgid "Departments"
msgid "Department"
msgstr "Departmanlar"

#: templates/jobs.html:61
#, fuzzy
#| msgid "Departments"
msgid "All Departments"
msgstr "Departmanlar"

#: templates/jobs.html:69 templates/people.html:35 templates/people.html:82
msgid "Status"
msgstr ""

#: templates/jobs.html:71 templates/people.html:37
msgid "All Statuses"
msgstr ""

#: templates/jobs.html:79 templates/people.html:45 templates/people.html:83
#, fuzzy
#| msgid "Locations"
msgid "Location"
msgstr "Konumlar"

#: templates/jobs.html:81 templates/people.html:47
#, fuzzy
#| msgid "Locations"
msgid "All Locations"
msgstr "Konumlar"

#: templates/jobs.html:89
msgid "Posted Date"
msgstr ""

#: templates/jobs.html:91
#, fuzzy
#| msgid "End Time"
msgid "All Time"
msgstr "Bitiş Zamanı"

#: templates/jobs.html:93 templates/people.html:59
#, fuzzy
#| msgid "Week"
msgid "This Week"
msgstr "Hafta"

#: templates/jobs.html:94 templates/people.html:60
#, fuzzy
#| msgid "Month"
msgid "This Month"
msgstr "Ay"

#: templates/jobs.html:95
#, fuzzy
#| msgid "Month"
msgid "Last Month"
msgstr "Ay"

#: templates/jobs.html:106 templates/people.html:70
msgid "Clear all filters"
msgstr ""

#: templates/jobs.html:134
#, fuzzy
#| msgid "Final Interview"
msgid "Interviews"
msgstr "Son görüşme"

#: templates/jobs.html:145
msgid "Days Open"
msgstr ""

#: templates/jobs.html:151
#, fuzzy
#| msgid "Closed"
msgid "Closed on:"
msgstr "Kapalı"

#: templates/jobs.html:151
msgid "Posted on:"
msgstr ""

#: templates/jobs.html:152
msgid "View Details"
msgstr ""

#: templates/jobs.html:162 templates/people.html:151
msgid "Showing"
msgstr ""

#: templates/jobs.html:162 templates/people.html:151
msgid "of"
msgstr ""

#: templates/jobs.html:162
msgid "jobs"
msgstr ""

#: templates/manage_permissions.html:11
msgid "Team & Invitations"
msgstr ""

#: templates/manage_permissions.html:12
msgid "Manage your recruitment team and invite new members"
msgstr ""

#: templates/manage_permissions.html:17 templates/manage_permissions.html:39
#: templates/settings.html:62
#, fuzzy
#| msgid "Locations"
msgid "Invitations"
msgstr "Konumlar"

#: templates/manage_permissions.html:29
msgid "Team Members"
msgstr ""

#: templates/manage_permissions.html:59
msgid "Administrators"
msgstr ""

#: templates/navbar.html:23
msgid "Feed"
msgstr "Ana Sayfa"

#: templates/navbar.html:41
msgid "Create Job"
msgstr "İlan Oluştur"

#: templates/navbar.html:121
msgid "Guest User"
msgstr ""

#: templates/navbar.html:128
msgid "Not logged in"
msgstr ""

#: templates/navbar.html:134
msgid "Profile"
msgstr "Profil"

#: templates/navbar.html:141
msgid "Logout"
msgstr "Çıkış Yap"

#: templates/people.html:8
msgid "Applicant Tracking"
msgstr "Aday Takibi"

#: templates/people.html:12
msgid "Search applicants..."
msgstr "Aday ara..."

#: templates/people.html:27
#, fuzzy
#| msgid "Position"
msgid "All Positions"
msgstr "Pozisyon"

#: templates/people.html:57
msgid "All Dates"
msgstr ""

#: templates/people.html:80
msgid "Name"
msgstr ""

#: templates/people.html:84
msgid "Experience (Years)"
msgstr ""

#: templates/people.html:85
msgid "Score"
msgstr ""

#: templates/people.html:86
#, fuzzy
#| msgid "applied for"
msgid "Applied On"
msgstr "başvurdu"

#: templates/people.html:87
#, fuzzy
#| msgid "Activity Feed"
msgid "Action"
msgstr "Aktif"

#: templates/people.html:134
#, fuzzy
#| msgid "Applicants"
msgid "View Application"
msgstr "Adaylar"

#: templates/people.html:141
msgid "No applicants found matching the current filters."
msgstr ""

#: templates/people.html:151
#, fuzzy
#| msgid "Applicants"
msgid "applicants"
msgstr "Adaylar"

#: templates/people.html:204
msgid "Show"
msgstr ""

#: templates/people.html:211
msgid "per page"
msgstr ""

#: templates/register.html:11
msgid "Accept Invitation"
msgstr ""

#: templates/register.html:15
msgid "This invitation has expired or already been used."
msgstr ""

#: templates/register.html:18
msgid "Hello"
msgstr ""

#: templates/register.html:18
msgid "you've been invited to join"
msgstr ""

#: templates/register.html:18
msgid "as a"
msgstr ""

#: templates/register.html:24 templates/signin.html:15
msgid "Email"
msgstr ""

#: templates/register.html:29
#, fuzzy
#| msgid "Create Job"
msgid "Create Password"
msgstr "İlan Oluştur"

#: templates/register.html:34
msgid "Confirm Password"
msgstr ""

#: templates/register.html:39
msgid "Complete Registration"
msgstr ""

#: templates/register.html:58
msgid "Passwords do not match"
msgstr ""

#: templates/settings.html:10
msgid "Configure your recruitment workflow and manage your ATS settings"
msgstr "İşe alım iş akışınızı yapılandırın ve ATS ayarlarınızı yönetin"

#: templates/settings.html:21
msgid ""
"Configure default options for job creation including work schedules, office "
"locations, and role titles."
msgstr ""

#: templates/settings.html:23
msgid "Define company work schedules"
msgstr ""

#: templates/settings.html:24
msgid "Set up office locations"
msgstr ""

#: templates/settings.html:25
msgid "Standardize role titles"
msgstr ""

#: templates/settings.html:26
msgid "Configure office schedule options"
msgstr ""

#: templates/settings.html:29
#, fuzzy
#| msgid "Preferences"
msgid "Manage Preferences"
msgstr "Tercihler"

#: templates/settings.html:41
msgid "Templates"
msgstr ""

#: templates/settings.html:42
#, fuzzy
#| msgid "Configure standard options to streamline your job creation process"
msgid ""
"Create, edit, and manage job description templates to streamline your job "
"posting process."
msgstr ""
"İş oluşturma sürecinizi kolaylaştırmak için standart seçenekleri yapılandırın"

#: templates/settings.html:44
msgid "Build reusable job templates"
msgstr ""

#: templates/settings.html:45
msgid "Save time on repetitive descriptions"
msgstr ""

#: templates/settings.html:46
msgid "Maintain consistent job postings"
msgstr ""

#: templates/settings.html:47
msgid "Organize templates by department"
msgstr ""

#: templates/settings.html:50
msgid "Manage Templates"
msgstr ""

#: templates/settings.html:63
msgid ""
"Invite team members to collaborate on your recruitment process and manage "
"user access."
msgstr ""

#: templates/settings.html:65
msgid "Add colleagues to your ATS"
msgstr ""

#: templates/settings.html:66
msgid "Set user permissions"
msgstr ""

#: templates/settings.html:67
msgid "Track invitation status"
msgstr ""

#: templates/settings.html:68
msgid "Manage team collaboration"
msgstr ""

#: templates/settings.html:71
#, fuzzy
#| msgid "Language Settings"
msgid "Manage Invitations"
msgstr "Dil Ayarları"

#: templates/settings.html:84
msgid "Job Portals"
msgstr ""

#: templates/settings.html:85
msgid ""
"Configure connections to external job boards and manage API credentials for "
"job publishing."
msgstr ""

#: templates/settings.html:87
msgid "Connect to major job boards"
msgstr ""

#: templates/settings.html:88
msgid "Manage API tokens securely"
msgstr ""

#: templates/settings.html:89
msgid "Customize portal preferences"
msgstr ""

#: templates/settings.html:90
msgid "Track portal integration status"
msgstr ""

#: templates/settings.html:93
msgid "(Coming Soon!)"
msgstr ""

#: templates/settings.html:103
msgid "Need Help?"
msgstr ""

#: templates/settings.html:104
msgid ""
"Our support team is ready to assist you with any questions about configuring "
"your ATS."
msgstr ""

#: templates/settings.html:105
msgid "Contact Support"
msgstr ""

#: templates/signin.html:16
msgid "<EMAIL>"
msgstr ""

#: templates/signin.html:20
msgid "Password"
msgstr ""

#: templates/signin.html:48
msgid "Signin"
msgstr ""

#~ msgid "A new vacancy"
#~ msgstr "Yeni iş ilanı"

#~ msgid "is published"
#~ msgstr "yayınlandı."

#~ msgid "moved to"
#~ msgstr "taşıdı"

#~ msgid "for"
#~ msgstr "için"

#, fuzzy
#~| msgid "Jobs"
#~ msgid "Hot Jobs"
#~ msgstr "Popüler İlanlar"

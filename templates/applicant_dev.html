{% extends 'main.html'%} {% load static %} {% load i18n %} {% block content %} {% load initials_avatar %}

<div class="container mx-auto px-4 py-6">
  <!-- Applicant Header with Profile -->
  <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
    <div class="p-6">
      <div class="flex flex-col md:flex-row items-start md:items-center">
        <div
          class="w-20 h-20 rounded-full mr-6 flex items-center justify-center text-white text-2xl font-semibold"
          style="background-color: {{ candidate_info.avatar_bg_color }}"
        >
          {{ candidate_info.candidate_firstname|first }}{{ candidate_info.candidate_lastname|first }}
        </div>

        <div class="mt-4 md:mt-0">
          <!-- Name and Position -->
          <div class="flex flex-col md:flex-row md:items-center">
            <h1 class="text-2xl font-bold text-gray-900">
              {{ candidate_info.full_name }}
            </h1>
            <span
              class="md:ml-3 px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800 font-medium"
            >
              {{ application.application_state }}
            </span>
          </div>

          <!-- Applied position -->
          <p class="text-lg text-gray-700 mt-1">
            {% trans "Applied for:" %}
            <span class="font-medium"
              >{{ application.vacancy_id.vacancy_title }}</span
            >
          </p>

          <!-- Contact details -->
          <div class="mt-3 flex flex-col sm:flex-row sm:items-center">
            <div class="flex items-center mr-6">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 text-gray-500 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
              <a
                href="mailto:{{ candidate_info.candidate_email }}"
                class="text-gray-600 hover:text-blue-600"
                >{{ candidate_info.candidate_email }}</a
              >
            </div>
            <div class="flex items-center mt-2 sm:mt-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 text-gray-500 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
              <a
                href="tel:{{ candidate_info.candidate_phone }}"
                class="text-gray-600 hover:text-blue-600"
                >{{ candidate_info.candidate_phone }}</a
              >
            </div>
          </div>
        </div>
        <!-- Action buttons -->
        <div class="mt-4 d-flex flex-column gap-2 ml-auto">
          <button
            id="add-event-btn"
            class="btn btn-primary d-flex align-items-center"
          >
            <i class="bi bi-calendar2-week mx-2"></i>
            Schedule Interview
          </button>

          <button
            class="btn btn-success d-flex align-items-center"
            id="change-status"
            data-bs-toggle="modal"
            data-bs-target="#status-change-modal"
          >
            <i class="bi bi-pencil-square mx-2"></i>
            Change State
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Tabs for different sections -->
  <div class="mb-6">
    <ul class="flex flex-wrap border-b border-gray-200">
      <li class="mr-2">
        <a
          href="#dashboard-ai"
          class="tab-link inline-block py-3 px-4 text-sm font-medium text-blue-600 border-b-2 border-blue-600 active"
          data-tab="dashboard-ai"
          >Dashboard & AI</a
        >
      </li>

      <li class="mr-2">
        <a
          href="#facts"
          class="tab-link inline-block py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300"
          data-tab="facts"
          >Candidate Background</a
        >
      </li>

      <li class="mr-2">
        <a
          href="#resume"
          class="tab-link inline-block py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300"
          data-tab="resume"
          >Resume</a
        >
      </li>

      <li class="mr-2">
        <a
          href="#journey"
          class="tab-link inline-block py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300"
          data-tab="journey"
          >Journey</a
        >
      </li>

      <li class="mr-2">
        <a
          href="#comments"
          class="tab-link inline-block py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300"
          data-tab="comments"
          >Comments</a
        >
      </li>

      <li class="mr-2">
        <a
          href="#emails"
          class="tab-link inline-block py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300"
          data-tab="emails"
          >Emails</a
        >
      </li>
      <li class="mr-2">
        <a
          href="#job-details"
          class="tab-link inline-block py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300"
          data-tab="job-details"
          >Job Details</a
        >
      </li>
    </ul>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">
    <!-- Combined Dashboard & AI Tab -->
    <div id="dashboard-ai" class="tab-pane active">
      <!-- AI Dashboard Content -->
      <div class="relative">
        <!-- Content that will be blurred or clear based on cv_text_obj.is_cv_analyzed -->
        <div
          id="ai-dashboard-content"
          class="grid grid-cols-1 lg:grid-cols-2 gap-6"
          style="filter: {% if cv_text_obj.is_cv_analyzed %}none{% else %}blur(8px){% endif %} !important; transition: filter 0.3s ease-in-out;"
        >
          <!-- Profile Match Analysis -->
          <div
            class="bg-white rounded-xl shadow-md overflow-hidden profile-match-analysis"
          >
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-800">
                Profile Match Analysis
              </h3>
            </div>
            <div class="p-6">
              <div class="space-y-4">
                <!-- Key Highlights section - will show if available -->
                <div>
                  <h4 class="text-md font-medium text-gray-800 mb-3">
                    Key Highlights
                  </h4>
                  <div id="ai-highlights-container" class="space-y-3">
                    {% if cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result.highlights %}
                    {% for highlight in cv_text_obj.ai_analysis_result.highlights %}
                    <div class="flex items-start">
                      <div
                        class="flex-shrink-0 h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mr-3"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3 w-3 text-green-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <p class="text-sm text-gray-700">{{ highlight }}</p>
                    </div>
                    {% endfor %} {% else %}
                    <div class="text-sm text-gray-500 italic">
                      AI analysis will provide candidate highlights.
                    </div>
                    {% endif %}
                  </div>
                </div>

                <!-- Drawbacks section - will show if available -->
                {% if cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result.drawbacks %}
                <div id="ai-drawbacks-section" class="mt-6">
                  <h4 class="text-md font-medium text-gray-800 mb-3">
                    Areas for Improvement
                  </h4>
                  <div id="ai-drawbacks-container" class="space-y-3">
                    {% for drawback in cv_text_obj.ai_analysis_result.drawbacks %}
                    <div class="flex items-start">
                      <div
                        class="flex-shrink-0 h-5 w-5 rounded-full bg-red-100 flex items-center justify-center mr-3"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3 w-3 text-red-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <p class="text-sm text-gray-700">{{ drawback }}</p>
                    </div>
                    {% endfor %}
                  </div>
                </div>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- AI Highlights Section -->
          <div
            class="bg-white rounded-xl shadow-md overflow-hidden candidate-highlights-section"
          >
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-800">
                Candidate Summary
              </h3>
            </div>
            <div class="p-6">
              <div class="mb-5">
                <div class="flex items-center">
                  <div class="relative w-32 h-32">
                    <svg class="w-full h-full" viewBox="0 0 36 36">
                      <!-- Background circle -->
                      <circle
                        cx="18"
                        cy="18"
                        r="16"
                        fill="none"
                        stroke="#e5e7eb"
                        stroke-width="2"
                      ></circle>
                      <!-- Progress circle -->
                      <circle
                        cx="18"
                        cy="18"
                        r="16"
                        fill="none"
                        stroke="#3b82f6"
                        stroke-width="2"
                        stroke-dasharray="100.53 100.53"
                        stroke-dashoffset="{% if cv_text_obj.is_cv_analyzed %}{{ cv_text_obj.ai_analysis_result.score|default:0 }}{% else %}100.53{% endif %}"
                        transform="rotate(-90 18 18)"
                        id="score-progress-circle"
                        data-score="{{ cv_text_obj.ai_analysis_result.score|default:0 }}"
                      ></circle>
                      <!-- Score text -->
                      <text
                        x="18"
                        y="18"
                        text-anchor="middle"
                        dominant-baseline="middle"
                        font-size="8"
                        font-weight="bold"
                        fill="#111827"
                        class="ai-score"
                      >
                        {{ cv_text_obj.ai_analysis_result.score|default:"0" }}
                      </text>
                    </svg>
                  </div>
                  <div class="ml-6" style="width: 70%">
                    <h6 class="font-medium text-gray-600 mb-1" id="ai-match-text">
                      {% if cv_text_obj.is_cv_analyzed %}
                        Based on the AI analysis, when the resume is compared to the job requirements,
                        {% if cv_text_obj.ai_analysis_result.score >= 80 %}
                          This candidate is an
                          <span class="font-bold text-green-600">excellent match</span>.
                        {% elif cv_text_obj.ai_analysis_result.score >= 60 %}
                          This candidate is a
                          <span class="font-bold text-blue-600">good match</span>.
                        {% elif cv_text_obj.ai_analysis_result.score >= 40 %}
                          This candidate is a
                          <span class="font-bold text-yellow-600">fair match</span>.
                        {% else %}
                          This candidate is a
                          <span class="font-bold text-red-600">weak match</span>.
                        {% endif %}
                      {% else %}
                        Analyze the CV with AI to see match details.
                      {% endif %}
                    </h6>
                  </div>
                </div>
              </div>
              <div>
                <!-- Add Summary Section if available-->
                <p id="ai-summary" class="text-sm text-gray-700 mt-2">
                  {% if cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result.summary %} {{ cv_text_obj.ai_analysis_result.summary }} {% endif %}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Analyze Button Overlay - only shown if is_cv_analyzed is false -->
        {% if not cv_text_obj.is_cv_analyzed %}
        <div
          id="button-overlay"
          class="absolute inset-0 flex items-center justify-center z-10"
        >
          <div
            class="text-center bg-white p-8 rounded-xl shadow-lg border-2 border-blue-200"
            style="background-color: rgba(255, 255, 255, 0.95)"
          >
            <h3 class="text-xl font-bold text-gray-800 mb-4">
              AI Analysis Available
            </h3>
            <p class="text-gray-700 mb-6">
              Leverage AI to analyze this candidate's CV against the job
              description.
            </p>
            <button
              id="analyze-with-ai"
              data-application-id="{{ application.application_id }}"
              class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-md flex items-center mx-auto transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                />
              </svg>
              Analyze with CanviderAI
            </button>
          </div>
        </div>
        {% endif %}

        <!-- Loading Indicator (hidden by default) -->
        <div
          id="ai-loading-indicator"
          class="hidden absolute inset-0 flex items-center justify-center z-20 bg-white/80"
        >
          <div class="text-center p-6 rounded-lg flex flex-col items-center">
            <div
              class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-4"
            ></div>
            <p class="text-lg font-medium text-gray-800">Analyzing CV...</p>
            <p class="text-sm text-gray-600 mt-2">This may take a moment</p>
          </div>
        </div>
      </div>

      <!-- Analysis complete indicator (shown if cv_text_obj.is_cv_analyzed is true) -->
      {% if cv_text_obj.is_cv_analyzed %}
      <div
        id="ai-analysis-complete-indicator"
        class="mt-4 text-sm font-medium text-green-600 flex items-center justify-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-1"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
        Analysis complete
      </div>
      {% else %}
      <div
        id="ai-analysis-complete-indicator"
        class="hidden mt-4 text-sm font-medium text-green-600 flex items-center justify-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-1"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
        Analysis complete
      </div>
      {% endif %}
    </div>

    <!-- End of AI Dashboard Content -->

    <!-- Candidate Facts Tab -->
    <div id="facts" class="tab-pane hidden">
      <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">Candidate Facts</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Column 1 -->
            <div>
              <div class="mb-4">
                <p class="text-sm text-gray-500">Applied Position</p>
                <p class="text-base font-medium text-gray-800">
                  {{ application.vacancy_id.vacancy_title }}
                </p>
              </div>
              <div class="mb-4">
                <p class="text-sm text-gray-500">Candidate's Address</p>
                <p class="text-base font-medium text-gray-800">
                  {% if cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result.location %}
                  {{ cv_text_obj.ai_analysis_result.location }} {% else %} Not
                  analyzed {% endif %}
                </p>
              </div>
              <div class="mb-4">
                <p class="text-sm text-gray-500">Application Date</p>
                <p class="text-base font-medium text-gray-800">
                  {{ application.application_date|date:"F j, Y" }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Application Portal</p>
                <p class="text-base font-medium text-gray-800">
                  {{ application.application_source }}
                </p>
              </div>
            </div>

            <!-- Column 2 -->
            <div>
              <div class="mb-4">
                <p class="text-sm text-gray-500">Latest/Current Position</p>
                <p class="text-base font-medium text-gray-800">
                  {% if cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result.latest_role %}
                  {{ cv_text_obj.ai_analysis_result.latest_role }} {% else %} Not
                  analyzed {% endif %}
                </p>
              </div>
              <div class="mb-4">
                <p class="text-sm text-gray-500">Latest/Current Employer</p>
                <p class="text-base font-medium text-gray-800">
                  {% if cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result.latest_employer %}
                  {{ cv_text_obj.ai_analysis_result.latest_employer }} {% else %} Not
                  analyzed {% endif %}
                </p>
              </div>
              <div class="mb-4">
                <p class="text-sm text-gray-500">Status</p>
                <p class="text-base font-medium text-gray-800">
                  {{ application.application_state }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Application ID</p>
                <p class="text-base font-medium text-gray-800">
                  #{{ application.application_id }}
                </p>
              </div>
            </div>

            <!-- Column 3 -->
            <div>
              <div class="mb-4">
                <p class="text-sm text-gray-500">Total Experience</p>
                <p class="text-base font-medium text-gray-800">
                  {% if cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result.total_experience %}
                  {{ cv_text_obj.ai_analysis_result.total_experience }} {% else %} Not
                  analyzed {% endif %}
                </p>
              </div>
              <div class="mb-4">
                <p class="text-sm text-gray-500">Education Level</p>
                <p class="text-base font-medium text-gray-800">
                  {% if cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result.latest_degree %}
                  {{ cv_text_obj.ai_analysis_result.latest_degree }} {% else %} Not
                  analyzed {% endif %}
                </p>
              </div>
              <div class="mb-4">
                <p class="text-sm text-gray-500">Notice Period</p>
                <p class="text-base font-medium text-gray-800">
                  {{ application.notice_period }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Latest Update</p>
                <p class="text-base font-medium text-gray-800">Placeholder</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Resume Tab -->
    <div id="resume" class="tab-pane hidden">
      <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">Resume</h3>
        </div>
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <p class="text-gray-600">
              Uploaded on {{ application.application_date|date:"F j, Y" }}
            </p>
          </div>

          <!-- Resume Preview -->
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <div
              class="p-8 bg-gray-50 min-h-[600px] flex flex-col items-center justify-center"
            >
              <div class="embed-responsive" style="padding-bottom: 100%">
                <object
                  data="{% static application.cv_location %}"
                  type="application/pdf"
                  width="100%"
                  height="100%"
                >
                  <p>
                    Your browser does not support PDFs.
                    <a
                      href="{% static application.cv_location %}"
                      target="_blank"
                      >Download the CV</a
                    >.
                  </p>
                </object>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Application Stages Tab -->
    <div id="journey" class="tab-pane hidden">
      <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">
            Application Stages
          </h3>
        </div>
        <div class="p-6">
          <ul class="space-y-4">
            {% for stage in stage_data %}
            <li class="flex items-start">
              <div
                class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-3"
              >
                <i class="bi bi-fullscreen-exit"></i>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">
                  {{ stage.get_state_name_display }}
                </h4>
                <p class="text-sm text-gray-500">
                  Started on: {{ stage.state_started_at|date:"F j, Y, g:i a" }}
                </p>
                {% if stage.state_notes %}
                <p class="text-sm text-gray-700 mt-1">
                  {{ stage.state_notes }}
                </p>
                {% endif %}
              </div>
            </li>
            <hr class="mx-5" />
            {% empty %}
            <li class="text-center text-gray-500 italic">
              No stages available for this application.
            </li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>

    <!-- Emails Tab -->
    <div id="emails" class="tab-pane hidden">
      <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">Emails</h3>
        </div>
        <div class="p-6">
          <!-- Email List -->
          <div class="mb-6">
            <h4 class="text-md font-medium text-gray-800 mb-3">
              Email History
            </h4>
            <div class="space-y-4">
              {% for email in emails_data %}
              <div
                class="bg-gray-50 rounded-lg p-4 {% if '@canvider.com' in email.from_ %}bg-blue-50{% endif %}"
              >
                <div class="flex justify-between items-start">
                  <div>
                    <p class="text-sm text-gray-500">
                      <strong>From:</strong> {{ email.from_ }} |
                      <strong>To:</strong> {{ email.to }}
                    </p>
                    <p class="text-sm text-gray-500">
                      <strong>Subject:</strong> {{ email.subject }}
                    </p>
                  </div>
                  <p class="text-xs text-gray-500">
                    {{ email.date|date:"F j, Y, g:i a" }}
                  </p>
                </div>
                <div class="mt-3 text-gray-700">
                  {% if email.html %} {{ email.html|safe }} {% else %}
                  <pre>{{ email.text }}</pre>
                  {% endif %}
                </div>
              </div>
              {% empty %}
              <div class="text-center py-6">
                <p class="text-gray-500">No emails found.</p>
              </div>
              {% endfor %}
            </div>
          </div>

          <!-- Send Email Form -->
          <h4 class="text-md font-medium text-gray-800 mb-3">Send Email</h4>
          <form action="{% url 'send_email' %}" method="POST">
            {% csrf_token %}
            <input
              type="hidden"
              name="application_id"
              value="{{ application.application_id }}"
            />
            <input
              type="hidden"
              name="employer_name"
              value="{{ application.employer_id.employer_name }}"
            />

            <div class="mb-3">
              <label for="email_subject" class="form-label">Subject</label>
              <input
                type="text"
                class="form-control"
                id="email_subject"
                name="subject"
                required
              />
            </div>
            <div class="mb-3">
              <label for="email_body" class="form-label">Email Body</label>
              <textarea
                id="email_body"
                name="body"
                rows="5"
                class="form-control"
                required
              ></textarea>
            </div>
            <!--- Add Hidden field for email_subject_key -->
            <input
              type="hidden"
              name="email_subject_key"
              value="{{ email_subject_key }}"
            />
            <input
              type="hidden"
              name="recipent"
              value="{{candidate_info.candidate_email}}"
            />

            <button type="submit" class="btn btn-primary">Send Email</button>
          </form>
        </div>
      </div>
    </div>

    <!-- Comments Tab -->
    <div id="comments" class="tab-pane hidden">
      <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">Comments</h3>
        </div>
        <div class="p-6">
          <!-- Comment form -->
          <form action="{% url 'insert_comment' %}" method="POST" class="mb-4">
            {% csrf_token %}
            <input
              type="hidden"
              name="application_id"
              value="{{ application.application_id }}"
            />
            <div class="mb-3">
              <label for="comment_body" class="form-label">Add a comment</label>
              <textarea
                id="comment_body"
                name="comment_body"
                rows="3"
                class="form-control"
                placeholder="Add your comment here..."
              ></textarea>
            </div>
            <div class="text-end">
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-send"></i> Post Comment
              </button>
            </div>
          </form>

          <!-- Comment list -->
          <div class="space-y-6">
            {% for comment in comments_data %}
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex justify-between items-start">
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 rounded-full bg-blue-100 mr-3 flex items-center justify-center text-blue-700 font-semibold"
                  >
                    <i class="bi bi-chat-square-dots"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-900">
                      {{ comment.commented_by }}
                    </h4>
                    <p class="text-xs text-gray-500">
                      {{ comment.comment_date|date:"F j, Y, g:i a" }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="mt-3 text-gray-700 mx-5 p-2">
                {{ comment.comment_body }}
              </div>
            </div>
            {% empty %}
            <div class="text-center py-6">
              <p class="text-gray-500">
                No comments yet. Be the first to comment!
              </p>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>

    <!-- Job Details Tab -->
    <div id="job-details" class="tab-pane hidden">
      <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">Job Details</h3>
        </div>
        <div class="p-6" id="job-description-area">
          {{ job_description|safe }}
        </div>
      </div>
    </div>
  </div>
</div>


 <!-- Event Creation Form Modal (initially hidden) -->
<div id="event-form-container" class="modal fade" tabindex="-1" aria-labelledby="eventFormModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="eventFormModalLabel">Schedule Interview</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="new-event-form">
          <div class="form-group mb-3">
            <label for="event-title" class="form-label">Event Title</label>
            <input
              type="text"
              id="event-title"
              class="form-control"
              placeholder="Enter event title"
              required
            />
          </div>

          <div class="form-group mb-3">
            <label for="event-type" class="form-label">Event Type</label>
            <select
              id="event-type"
              class="form-select"
              required
            >
              <option value="">Select an event type</option>
              {% for kind in appointment_form_data.appointment_kinds %}
              <option value="{{ kind }}">{{ kind }}</option>
              {% endfor %}
            </select>
          </div>

          <div class="form-group mb-3">
            <label for="recruiters-dropdown" class="form-label">Recruiters</label>
            <select
              id="recruiters-dropdown"
              class="form-select"
            >
              <option value="">Select one or many recruiters</option>
              {% for recruiter in appointment_form_data.possible_interviewers %}
              <option value="{{ recruiter }}">{{ recruiter }}</option>
              {% endfor %}
            </select>
            <div id="selected-recruiters" class="selected-recruiters mt-2"></div>
            <input type="hidden" id="recruiters-input" name="recruiters" />
          </div>

          <div class="form-group mb-3" id="vacancy-group">
            <label for="vacancy" class="form-label">Position</label>
            <select
              id="vacancy"
              disabled
              class="form-select"
              name="vacancy"
              hx-get="{% url 'get_candidates_for_vacancy' %}"
              hx-target="#candidates-dropdown"
              hx-trigger="change"
            >
              <option value="{{ application.vacancy_id.vacancy_id }}">
                {{ application.vacancy_id.vacancy_title }}
              </option>
            </select>
          </div>

          <div class="form-group mb-3">
            <label for="candidates-dropdown" class="form-label">Candidate</label>
            <select
              disabled
              id="candidates-dropdown"
              class="form-select"
              name="candidates"
            >
              <option value="{{  candidate_info.candidate_id }}" selected>
                {{ candidate_info.full_name }}
              </option>
            </select>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <div class="form-group">
                <label for="event-date" class="form-label">Date</label>
                <input type="date" id="event-date" class="form-control" required />
              </div>
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <div class="form-group">
                <label for="event-start-time" class="form-label">Start Time</label>
                <input type="time" id="event-start-time" class="form-control" required />
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="event-end-time" class="form-label">End Time</label>
                <input type="time" id="event-end-time" class="form-control" required />
              </div>
            </div>
          </div>

          <div class="form-group mb-3">
            <label for="event-link" class="form-label">Meeting Link</label>
            <input
              type="text"
              id="event-link"
              class="form-control"
              placeholder="Enter meeting link"
            />
          </div>

          <div class="form-check form-switch mb-3">
            <input
              class="form-check-input"
              type="checkbox"
              id="inform_invitees"
              name="inform_invitees"
              value="true"
              checked
            />
            <label class="form-check-label" for="inform_invitees">
              Inform invitees by E-mail
            </label>
          </div>

          <div class="form-group mb-3">
            <label for="event-color" class="form-label">Color</label>
            <select id="event-color" class="form-select">
              <option value="1">Blue</option>
              <option value="2">Light Blue</option>
              <option value="3">Purple</option>
              <option value="4">Pink</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" id="cancel-event-btn" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" id="save-event-btn" class="btn btn-primary">
          Save Event
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const dropdown = document.getElementById("recruiters-dropdown");
    const selectedRecruiters = document.getElementById("selected-recruiters");
    const recruitersInput = document.getElementById("recruiters-input");
    const selectedNames = new Set();

    dropdown.addEventListener("change", function () {
      const selectedName = this.value;
      if (selectedName && !selectedNames.has(selectedName)) {
        selectedNames.add(selectedName);
        updateSelectedRecruiters();
      }
      this.selectedIndex = 0;
    });

    function updateSelectedRecruiters() {
      selectedRecruiters.innerHTML = "";
      recruitersInput.value = Array.from(selectedNames).join(", ");

      selectedNames.forEach((name) => {
        const nameSpan = document.createElement("span");
        nameSpan.textContent = name;
        nameSpan.className = "selected-name";

        const removeButton = document.createElement("button");
        removeButton.textContent = "x";
        removeButton.className = "remove-name";
        removeButton.onclick = function () {
          selectedNames.delete(name);
          updateSelectedRecruiters();
        };

        nameSpan.appendChild(removeButton);
        selectedRecruiters.appendChild(nameSpan);
      });
    }
  });
</script>

<!-- Status Change Modal -->
<div
  id="status-change-modal"
  class="modal fade"
  tabindex="-1"
  aria-labelledby="statusChangeModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="statusChangeModalLabel">
          Change Application Status
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <form method="POST" action="{% url 'change_state' %}">
          {% csrf_token %}
          <input
            type="hidden"
            name="application_id"
            value="{{ application.application_id }}"
          />
          <div class="mb-3">
            <label for="new-status" class="form-label">New Status</label>
            <select id="new-status" name="state_name" class="form-select">
              <option value="New">New</option>
              <option value="Review_1">Review #1</option>
              <option value="Review_2">Review #2</option>
              <option value="Review_3">Review #3</option>
              <option value="Review_4">Review #4</option>
              <option value="Review_5">Review #5</option>
              <option value="Decision">Ready for Decision</option>
              <option value="Eliminated">Eliminated</option>
              <option value="Offer">Offer Made</option>
              <option value="Accept">Candidate Accepted</option>
              <option value="Reject">Candidate Rejected</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="status-note" class="form-label">Internal Notes <small class="text-muted">(visible only to recruiters)</small></label>
            <textarea
              required
              id="status-note"
              name="state_notes"
              rows="3"
              class="form-control"
              placeholder="Add internal notes about this status change..."
            ></textarea>
          </div>

          <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="notify-candidate" name="notify_candidate" checked>
            <label class="form-check-label" for="notify-candidate">
              Notify candidate about this status change via email
            </label>
          </div>

          <div id="email-notification-section" class="mb-3 border rounded p-3" style="background-color: #f8f9fa;">
            <div class="mb-3">
              <label for="email-message" class="form-label">Email Message <small class="text-muted">(will be included in the email to the candidate)</small></label>
              <textarea
                id="email-message"
                name="email_message"
                rows="3"
                class="form-control"
                placeholder="Add a personalized message for the candidate..."
              ></textarea>
            </div>

            <div id="email-preview-container" class="mb-3 p-3 border rounded bg-light">
              <h6 class="mb-2">Email Preview</h6>
              <div id="email-preview">
                <p><strong>Subject:</strong> <span id="email-subject-preview">Your application status has been updated</span></p>
                <hr>
                <div id="email-body-preview">

                  <p>Dear {{ candidate_info.full_name }},</p>
                  <span id="email-body-head">
                  <p>We would like to inform you that your application for <strong>{{ application.vacancy_id.vacancy_title }}</strong> has been updated to <strong> <span id="new-status-preview">${newStatus}</span> </strong>.</p>
                  </span>
                  <br>

                  <p><span id="email-message-preview">Your personalized message will appear here.</span></p>
                  <p>If you have any questions, please don't hesitate to contact us.</p>

                  <br>

                  <p>Best regards,<br>{{ application.employer_id.employer_name }} Recruitment Team</p>

                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="submit" class="btn btn-primary">Save Change</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>




<style>
  /* Ensure blur effect is applied correctly */
  #ai-dashboard-content.blurred {
    filter: blur(8px) !important;
  }

  #ai-dashboard-content.not-blurred {
    filter: none !important;
  }

  /* Style the button overlay for better visibility */
  #button-overlay .text-center {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.98),
      rgba(240, 249, 255, 0.98)
    );
  }

  /* Modal styles */
  .modal-content {
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  }

  .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  .modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
  }

  /* Selected recruiters styling */
  .selected-recruiters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
  }

  .selected-name {
    display: inline-flex;
    align-items: center;
    background-color: #e9ecef;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 14px;
  }

  .remove-name {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    margin-left: 5px;
    font-weight: bold;
    padding: 0 5px;
  }

  .remove-name:hover {
    color: #dc3545;
  }

  /* Email preview styling */
  #email-preview-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  #email-preview {
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
  }

  #email-subject-preview {
    font-weight: bold;
  }

  #email-body-preview {
    padding: 10px;
    background-color: white;
    border-radius: 4px;
  }

  #email-message-preview {
    display: block;
    padding: 8px;
    background-color: #f0f0f0;
    border-left: 3px solid #6c757d;
    margin: 10px 0;
  }

  #email-notification-section {
    transition: all 0.3s ease;
  }
</style>

<!-- JavaScript for tab functionality and interactive elements -->
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Tab functionality
    const tabLinks = document.querySelectorAll(".tab-link");
    const tabPanes = document.querySelectorAll(".tab-pane");
    // Modal elements
    const addEventBtn = document.getElementById("add-event-btn");
    const eventFormContainer = document.getElementById("event-form-container");
    const newEventForm = document.getElementById("new-event-form");
    const cancelEventBtn = document.getElementById("cancel-event-btn");
    const saveEventBtn = document.getElementById("save-event-btn");
    const eventTitleInput = document.getElementById("event-title");
    const eventDateInput = document.getElementById("event-date");
    const eventStartTimeInput = document.getElementById("event-start-time");
    const eventEndTimeInput = document.getElementById("event-end-time");
    const eventTypeSelect = document.getElementById("event-type");
    const eventLinkInput = document.getElementById("event-link");
    const eventColorSelect = document.getElementById("event-color");
    const informInviteesCheckbox = document.getElementById("inform_invitees");

    // Initialize the date field with today's date
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    if (eventDateInput) {
      eventDateInput.value = formattedDate;
    }

    // Open modal when "Schedule Interview" button is clicked
    if (addEventBtn) {
      addEventBtn.addEventListener("click", function() {
        const modal = new bootstrap.Modal(eventFormContainer);
        modal.show();
      });
    }

    // Handle form submission
    if (saveEventBtn) {
      saveEventBtn.addEventListener("click", function() {
        // Validate form
        if (!newEventForm.checkValidity()) {
          newEventForm.reportValidity();
          return;
        }

        // Get form values
        const title = eventTitleInput.value;
        const eventType = eventTypeSelect.value;
        const date = eventDateInput.value;
        const startTime = eventStartTimeInput.value;
        const endTime = eventEndTimeInput.value;
        const meetingLink = eventLinkInput.value;
        const color = eventColorSelect.value;
        const informInvitees = informInviteesCheckbox.checked;
        const recruiters = document.getElementById("recruiters-input").value;
        const vacancy = document.getElementById("vacancy").value;
        const candidate = document.getElementById("candidates-dropdown").value;

        // Create start and end datetime objects
        const startDateTime = new Date(`${date}T${startTime}`);
        const endDateTime = new Date(`${date}T${endTime}`);

        // Create event data object
        const eventData = {
          title: title,
          start: startDateTime,
          end: endDateTime,
          event_type: eventType,
          meeting_link: meetingLink,
          color: color,
          inform_invitees: informInvitees,
          recruiters: recruiters,
          vacancy: vacancy,
          candidate: candidate
        };

        // Save button state
        const originalText = saveEventBtn.textContent;
        saveEventBtn.textContent = "Saving...";
        saveEventBtn.disabled = true;

        // Send data to server
        fetch("/api/add_appointment/", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": getCsrfToken(),
          },
          body: JSON.stringify({
            title: title,
            start_time: startDateTime.toISOString(),
            end_time: endDateTime.toISOString(),
            recruiters: recruiters,
            vacancy: vacancy,
            candidate: candidate,
            event_type: eventType,
            meeting_link: meetingLink,
            inform_invitees: informInvitees,
            color: color
          }),
        })
        .then(response => response.json())
        .then(result => {
          if (result.success) {
            // Hide the modal
            const modal = bootstrap.Modal.getInstance(eventFormContainer);
            modal.hide();

            // Reset the form
            newEventForm.reset();

            // Show success message with toast notification
            showNotification("Interview scheduled successfully!", "bg-success");

            // Reload the page to show updated data after a short delay
            setTimeout(() => {
              location.reload();
            }, 1500);
          } else {
            showNotification("Failed to schedule the interview: " + (result.message || "Unknown error"), "bg-danger");
          }

          // Reset button state
          saveEventBtn.textContent = originalText;
          saveEventBtn.disabled = false;
        })
        .catch(error => {
          console.error("Error scheduling interview:", error);
          showNotification("An error occurred while scheduling the interview.", "bg-danger");

          // Reset button state
          saveEventBtn.textContent = originalText;
          saveEventBtn.disabled = false;
        });
      });
    }

    // Helper function to get CSRF token
    function getCsrfToken() {
      const cookieValue = document.cookie
        .split("; ")
        .find((row) => row.startsWith("csrftoken="))
        ?.split("=")[1];
      return cookieValue || "";
    }

    // Helper function to show notifications
    function showNotification(message, bgClass = "bg-primary") {
      // Create notification element
      const notification = document.createElement("div");
      notification.classList.add(
        "position-fixed", "top-0", "end-0", "p-3", "m-3",
        bgClass, "text-white", "rounded", "shadow", "notification-toast"
      );
      notification.style.zIndex = "9999";
      notification.style.minWidth = "300px";

      // Add content
      notification.innerHTML = `
        <div class="d-flex align-items-center">
          <div class="me-3">
            <i class="bi bi-check-circle-fill fs-4"></i>
          </div>
          <div>
            <p class="mb-0">${message}</p>
          </div>
        </div>
      `;

      // Add to document
      document.body.appendChild(notification);

      // Remove after 3 seconds
      setTimeout(() => {
        notification.style.opacity = "0";
        notification.style.transition = "opacity 0.5s";
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 500);
      }, 3000);
    }

    tabLinks.forEach((link) => {
      link.addEventListener("click", function (e) {
        e.preventDefault();

        // Remove active class from all tabs
        tabLinks.forEach((link) => {
          link.classList.remove("text-blue-600", "border-blue-600", "active");
          link.classList.add("text-gray-500", "border-transparent");
        });

        // Add active class to clicked tab
        this.classList.remove("text-gray-500", "border-transparent");
        this.classList.add("text-blue-600", "border-blue-600", "active");

        // Hide all tab panes
        tabPanes.forEach((pane) => {
          pane.classList.add("hidden");
          pane.classList.remove("active");
        });

        // Show the corresponding tab pane
        const tabId = this.getAttribute("data-tab");
        const tabPane = document.getElementById(tabId);
        tabPane.classList.remove("hidden");
        tabPane.classList.add("active");
      });
    });

    function calculateDashOffset(score) {
      const circumference = 100.53;
      return circumference - ((score / 100) * circumference);
    }

    // Function to calculate color based on score
    function getColorForScore(score) {
      // Normalize score to 0-1 range
      const normalized = score / 100;

      // Define color stops
      const red = { r: 239, g: 68, b: 68 };    // #ef4444
      const yellow = { r: 234, g: 179, b: 8 };  // #eab308
      const green = { r: 34, g: 197, b: 94 };   // #22c55e

      let r, g, b;

      if (normalized <= 0.5) {
        // Transition from red to yellow
        const factor = normalized * 2;
        r = red.r + (yellow.r - red.r) * factor;
        g = red.g + (yellow.g - red.g) * factor;
        b = red.b + (yellow.b - red.b) * factor;
      } else {
        // Transition from yellow to green
        const factor = (normalized - 0.5) * 2;
        r = yellow.r + (green.r - yellow.r) * factor;
        g = yellow.g + (green.g - yellow.g) * factor;
        b = yellow.b + (green.b - yellow.b) * factor;
      }

      return `rgb(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)})`;
    }

    function updateCircleColor(score) {
      const progressCircle = document.getElementById('score-progress-circle');
      if (progressCircle) {
        progressCircle.style.stroke = getColorForScore(score);
      }
    }



    // Status change modal functionality
    const newStatusSelect = document.getElementById('new-status');
    const statusNoteTextarea = document.getElementById('status-note');
    const emailMessageTextarea = document.getElementById('email-message');
    const notifyCandidateCheckbox = document.getElementById('notify-candidate');
    const emailNotificationSection = document.getElementById('email-notification-section');
    const newStatusPreview = document.getElementById('new-status-preview');
    const emailMessagePreview = document.getElementById('email-message-preview');
    const emailBodyHead = document.getElementById('email-body-head');

    // Update email preview when status changes
    if (newStatusSelect && newStatusPreview) {
      newStatusSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        newStatusPreview.textContent = selectedOption.text;
      });

      // Initialize with the current selected value
      if (newStatusPreview && newStatusSelect.selectedIndex >= 0) {
        const selectedOption = newStatusSelect.options[newStatusSelect.selectedIndex];
        newStatusPreview.textContent = selectedOption.text;
      }
    }


    // if the new state is "Eliminated" then change the mail content other than the default one
    if (newStatusSelect && emailBodyHead) {
      newStatusSelect.addEventListener('change', function() {
        if (this.value === 'Eliminated') {
          emailBodyHead.innerHTML = '<p>We regret to inform you that your application for <strong>{{application.vacancy_id.vacancy_title}}</strong> has not been successful at this time.</p>';
        } else {
          const selectedOption = this.options[this.selectedIndex];
          emailBodyHead.innerHTML = `<p>We would like to inform you that your application for <strong>{{ application.vacancy_id.vacancy_title }}</strong> has been updated to <strong>${selectedOption.text}</strong>.</p>`;
        }
      });
    }

    // Update email preview when email message changes
    if (emailMessageTextarea && emailMessagePreview) {
      emailMessageTextarea.addEventListener('input', function() {
        emailMessagePreview.textContent = this.value || 'Your personalized message will appear here.';
      });
    }


    // Toggle email notification section visibility based on checkbox
    if (notifyCandidateCheckbox && emailNotificationSection) {
      // Set initial state
      emailNotificationSection.style.display = notifyCandidateCheckbox.checked ? 'block' : 'none';

      // Add change event listener
      notifyCandidateCheckbox.addEventListener('change', function() {
        emailNotificationSection.style.display = this.checked ? 'block' : 'none';
      });
    }

    // Check if the CV has already been analyzed based on Django template variable
    const isCvAnalyzed = {% if cv_text_obj.is_cv_analyzed %}true{% else %}false{% endif %};

    if (isCvAnalyzed) {
      const initialScore = {{ cv_text_obj.ai_analysis_result.score|default:0 }};
      const progressCircle = document.getElementById('score-progress-circle');
      const scoreBar = document.querySelector('.ai-score-bar');
      if (progressCircle) {
        progressCircle.style.stroke = getColorForScore(initialScore);
        progressCircle.setAttribute('stroke-dashoffset', calculateDashOffset(initialScore));
      }
      if (scoreBar) {
        scoreBar.style.backgroundColor = getColorForScore(initialScore);
      }
    }

    // Apply appropriate styling based on analysis status
    const aiDashboardContent = document.getElementById("ai-dashboard-content");
    if (aiDashboardContent) {
      if (isCvAnalyzed) {
        aiDashboardContent.classList.remove("blurred");
        aiDashboardContent.classList.add("not-blurred");
        aiDashboardContent.style.filter = "none";
      } else {
        aiDashboardContent.classList.add("blurred");
        aiDashboardContent.style.filter = "blur(8px)";
      }
    }

    const analyzeButton = document.getElementById("analyze-with-ai");

    if (analyzeButton) {
      analyzeButton.addEventListener("click", function () {
        // Get the application ID from the data attribute
        const applicationId = analyzeButton.getAttribute("data-application-id");

        // Get elements
        const aiDashboardContent = document.getElementById("ai-dashboard-content");
        const aiLoadingIndicator = document.getElementById("ai-loading-indicator");
        const buttonOverlay = document.getElementById("button-overlay");

        // Hide button overlay and show loading indicator
        if (buttonOverlay) {
          buttonOverlay.style.display = "none";
        }

        if (aiLoadingIndicator) {
          aiLoadingIndicator.classList.remove("hidden");
        }

        // Display notification
        const notification = document.createElement("div");
        notification.classList.add(
          "fixed", "top-4", "right-4", "bg-blue-500",
          "text-white", "p-4", "rounded-lg", "shadow-lg",
          "z-50", "animate-pulse"
        );
        notification.innerHTML = "Analysis will be ready in a moment...";
        document.body.appendChild(notification);

        // Get CSRF token
        function getCookie(name) {
          let cookieValue = null;
          if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
              const cookie = cookies[i].trim();
              if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
              }
            }
          }
          return cookieValue;
        }

        const csrftoken = getCookie('csrftoken');
        // Make the API call to analyze the CV
        fetch(`/ai/analyze/${applicationId}/`, {
          method: "POST",
          headers: {
            "X-CSRFToken": csrftoken,
            "Content-Type": "application/json"
          }
        })
        .then(response => {
          if (!response.ok) {
            throw new Error("Network response was not ok: " + response.status);
          }
          return response.json();
        })
        .then(data => {
          // Remove the notification
          document.body.removeChild(notification);

          if (data.success) {
            // Update the UI with the analysis results
            updateAnalysisUI(data.analysis);

            // Show success notification
            showNotification("Analysis completed successfully!", "bg-green-500");

            // Remove blur effect to reveal content
            if (aiDashboardContent) {
              // Remove blur with both class and inline style for maximum compatibility
              aiDashboardContent.classList.remove("blurred");
              aiDashboardContent.classList.add("not-blurred");
              aiDashboardContent.style.filter = "none";
            }
            location.reload();
          } else {
            // Show error notification
            showNotification(data.message || "An error occurred during analysis", "bg-red-500");

            // Show the button overlay again
            if (buttonOverlay) {
              buttonOverlay.style.display = "flex";
            }
          }

          // Hide loading indicator
          if (aiLoadingIndicator) {
            aiLoadingIndicator.classList.add("hidden");
          }

          // Show that analysis is complete
          const analysisCompleteIndicator = document.getElementById("ai-analysis-complete-indicator");
          if (analysisCompleteIndicator) {
            analysisCompleteIndicator.classList.remove("hidden");
          }
        })
        .catch(error => {
          console.error("Error:", error);

          // Remove the notification
          document.body.removeChild(notification);

          // Show error notification
          showNotification("Failed to analyze CV: " + error.message, "bg-red-500");

          // Show the button overlay again
          if (buttonOverlay) {
            buttonOverlay.style.display = "flex";
          }

          // Hide loading indicator
          if (aiLoadingIndicator) {
            aiLoadingIndicator.classList.add("hidden");
          }
        });
      });
    }

    // Function to update the UI with analysis results
    function updateAnalysisUI(analysis) {
      // Update the score
      updateCircleColor(analysis.score);
      const scoreElements = document.querySelectorAll(".ai-score");
      scoreElements.forEach(element => {
        element.textContent = analysis.score;
      });

      // Update the score progress bars
      const scoreProgressBars = document.querySelectorAll(".ai-score-bar");
      scoreProgressBars.forEach(bar => {
        bar.style.width = analysis.score;
        bar.style.backgroundColor = getColorForScore(analysis.score);
      });

      // Update the score circle
      const scoreCircle = document.getElementById('score-progress-circle');
      if (scoreCircle) {
        scoreCircle.setAttribute('stroke-dashoffset', calculateDashOffset(analysis.score));
        scoreCircle.style.stroke = getColorForScore(analysis.score);
      }

      // Update the match text based on score
      const matchTextElement = document.getElementById("ai-match-text");
      if (matchTextElement) {
        let matchText = "";
        const color = getColorForScore(analysis.score);

        if (analysis.score >= 80) {
          matchText = "excellent match";
        } else if (analysis.score >= 60) {
          matchText = "good match";
        } else if (analysis.score >= 40) {
          matchText = "fair match";
        } else {
          matchText = "weak match";
        }

        matchTextElement.innerHTML = `Based on the AI analysis, when the resume is compared to the job requirements, This candidate is a <span class="font-bold" style="color: ${color};">${matchText}</span>.`;
      }

      // Add summary if it exists
      if (analysis.summary) {
        const summaryElement = document.getElementById("ai-summary");
        if (summaryElement) {
          summaryElement.textContent = analysis.summary;
        }
      }

      // Update the highlights
      const highlightsContainer = document.getElementById("ai-highlights-container");
      if (highlightsContainer) {
        highlightsContainer.innerHTML = ""; // Clear existing highlights

        if (analysis.highlights && analysis.highlights.length > 0) {
          analysis.highlights.forEach(highlight => {
            const highlightEl = document.createElement("div");
            highlightEl.classList.add("flex", "items-start");
            highlightEl.innerHTML = `
              <div class="flex-shrink-0 h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <p class="text-sm text-gray-700">${highlight}</p>
            `;
            highlightsContainer.appendChild(highlightEl);
          });
        } else {
          highlightsContainer.innerHTML = `
            <div class="text-sm text-gray-500 italic">
              No highlights available for this candidate.
            </div>
          `;
        }
      }

      // Update drawbacks
      const drawbacksContainer = document.getElementById("ai-drawbacks-container");
      if (drawbacksContainer) {
        drawbacksContainer.innerHTML = ""; // Clear existing drawbacks

        if (analysis.highlights && analysis.highlights.length > 0) {
          analysis.drawbacks.forEach(drawback => {
            const drawbackEl = document.createElement("div");
            drawbackEl.classList.add("flex", "items-start");
            drawbackEl.innerHTML = `
              <div class="flex-shrink-0 h-5 w-5 rounded-full bg-red-100 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </div>
              <p class="text-sm text-gray-700">${drawback}</p>
            `;
            drawbacksContainer.appendChild(drawbackEl);
          });
        } else {
          drawbacksContainer.innerHTML = `
            <div class="text-sm text-gray-500 italic">
              No drawbacks available for this candidate.
            </div>
          `;
        }
      }
    }

    // Function to show notification
    function showNotification(message, bgClass = "bg-blue-500") {
      const notification = document.createElement("div");
      notification.classList.add(
        "fixed", "top-4", "right-4",
        bgClass, "text-white", "p-4",
        "rounded-lg", "shadow-lg", "z-50"
      );
      notification.innerHTML = message;
      document.body.appendChild(notification);

      // Remove the notification after 4 seconds
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 4000);
    }


      // Add event button click handler
      if (addEventBtn) {
        console.log("Add event button found");
        addEventBtn.addEventListener("click", function () {
          // Reset selectedEvent since we're creating a new event
          selectedEvent = null;

          // Clear the form
          newEventForm.reset();

          // Show the event form
          // eventFormContainer.style.display = "block";
          // addEventBtn.style.display = "none";

          // Set default times (current time + 1 hour)
          const now = new Date();
          const startTime =
            now.getHours().toString().padStart(2, "0") +
            ":" +
            now.getMinutes().toString().padStart(2, "0");
          const endTime =
            (now.getHours() + 1).toString().padStart(2, "0") +
            ":" +
            now.getMinutes().toString().padStart(2, "0");

          eventStartTimeInput.value = startTime;
          eventEndTimeInput.value = endTime;
        });
      }

      // Add event button click handler
      if (addEventBtn) {
        addEventBtn.addEventListener("click", function () {
          // Reset selectedEvent since we're creating a new event
          selectedEvent = null;

          // Clear the form
          newEventForm.reset();

          // Show the event form
          eventFormContainer.style.display = "block";
          addEventBtn.style.display = "none";

          // Set default times (current time + 1 hour)
          const now = new Date();
          const startTime =
            now.getHours().toString().padStart(2, "0") +
            ":" +
            now.getMinutes().toString().padStart(2, "0");
          const endTime =
            (now.getHours() + 1).toString().padStart(2, "0") +
            ":" +
            now.getMinutes().toString().padStart(2, "0");

          eventStartTimeInput.value = startTime;
          eventEndTimeInput.value = endTime;
        });
      }



  });
</script>

{% endblock content %}

{% extends 'main.html' %} {% load static %} {% block content%}
<div class="container">
  <h1>Create Job Position</h1>
  <div class="form-container">
    <div class="form-row">
      <!-- First Column -->
      <div class="form-column">
        <h2>Basic Information</h2>
        <div class="form-group">
          <label for="position-title">Role Title</label>
          <input
            type="text"
            id="position-title"
            placeholder="e.g. Senior Software Engineer"
          />
        </div>
        <div class="form-group">
          <label for="office-location">Office Location</label>
          {% if has_locations %}
          <select id="office-location">
            <option value="">Select office location</option>
            {% for location in office_locations %}
            <option value="{{ location.city }}, {{ location.country }}">
              {{ location.city }}, {{ location.country }}
            </option>
            {% endfor %}
          </select>
          {% else %}
          <div class="no-data-message">
            <p>
              No office locations found. Please
              <a href="{% url 'preferences' %}">add office locations</a> in your
              preferences first.
            </p>
          </div>
          <select id="office-location" disabled>
            <option value="">No locations available</option>
          </select>
          {% endif %}
        </div>
        <div class="form-group">
          <label for="work-schedule">Work Schedule</label>
          {% if has_work_schedules %}
          <select id="work-schedule">
            <option value="">Select an option</option>
            {% for schedule in work_schedules %}
            <option value="{{ schedule.name }}">{{ schedule.name }}</option>
            {% endfor %}
          </select>
          {% else %}
          <div class="no-data-message">
            <p>
              No work schedules found. Please
              <a href="{% url 'preferences' %}">add work schedules</a> in your
              preferences first.
            </p>
          </div>
          <select id="work-schedule" disabled>
            <option value="">No work schedules available</option>
          </select>
          {% endif %}
        </div>
        <div class="form-group">
          <label for="office-schedule">Office Schedule</label>
          {% if has_office_schedules %}
          <select id="office-schedule">
            <option value="">Select an option</option>
            {% for schedule in office_schedules %}
            <option value="{{ schedule.name }}">{{ schedule.name }}</option>
            {% endfor %}
          </select>
          {% else %}
          <div class="no-data-message">
            <p>
              No office schedules found. Please
              <a href="{% url 'preferences' %}">add office schedules</a> in your
              preferences first.
            </p>
          </div>
          <select id="office-schedule" disabled>
            <option value="">No office schedules available</option>
          </select>
          {% endif %}
        </div>
      </div>

      <!-- Second Column -->
      <div class="form-column">
        <h2>Skills Requirements</h2>
        <div class="skills-container">
          <label for="role-title">Skill</label>
          <div class="skills-input-group">
            <input
              type="text"
              id="skills-input"
              placeholder="e.g. JavaScript"
            />
            <button class="add-btn" id="add-skill-btn">Add</button>
          </div>

          <label>Choose Skills</label>
          <div class="skill-chips">
            <div class="skill-chip" data-skill="JavaScript">JavaScript</div>
            <div class="skill-chip" data-skill="Python">Python</div>
            <div class="skill-chip" data-skill="Java">Java</div>
            <div class="skill-chip" data-skill="C++">C++</div>
            <div class="skill-chip" data-skill="C#">C#</div>
            <div class="skill-chip" data-skill="React">React</div>
            <div class="skill-chip" data-skill="Node.js">Node.js</div>
            <div class="skill-chip" data-skill="SQL">SQL</div>
            <div class="skill-chip" data-skill="AWS">AWS</div>
            <div class="skill-chip" data-skill="Docker">Docker</div>
          </div>

          <label>Selected Skills</label>
          <div class="selected-skills" id="selected-skills">
            <div class="empty-message" id="empty-skills-message">
              No skills selected yet
            </div>
            <!-- Selected skills will appear here -->
          </div>
        </div>
      </div>
    </div>

    <!-- Salary Details -->
    <div class="salary-container mt-4">
      <h2>Salary Details (Optional)</h2>
      <div class="salary-grid">
        <div class="form-group">
          <label for="salary-min">Minimum Salary</label>
          <input
            type="number"
            id="salary-min"
            placeholder="Enter minimum salary"
          />
        </div>
        <div class="form-group">
          <label for="salary-max">Maximum Salary</label>
          <input
            type="number"
            id="salary-max"
            placeholder="Enter maximum salary"
          />
        </div>
        <div class="form-group">
          <label for="salary-currency">Currency</label>
          <select id="salary-currency">
            <option value="">Select currency</option>
            <option value="USD">USD</option>
            <option value="EUR">EUR</option>
            <option value="PLN">PLN</option>
            <option value="TRY">TRY</option>
            <option value="GBP">GBP</option>
            <option value="JPY">JPY</option>
            <option value="AUD">AUD</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Job Benefits & Highlights -->

    <div class="benefit-container">
      <h2 for="benefits-input">Benefits and Highlights (Optional)</h2>
      <div class="skills-input-group">
        <input
          type="text"
          id="benefits-input"
          placeholder="e.g. Yearly Bonuses"
        />
        <button class="add-btn" id="add-benefit-btn">Add</button>
      </div>

      <label>Choose Benefits</label>
      <div class="benefit-chips">
        <div class="benefit-chip" data-benefit="Dental Coverage">
          Dental Coverage
        </div>
        <div class="benefit-chip" data-benefit="Private Health Coverage">
          Private Health Coverage
        </div>
        <div class="benefit-chip" data-benefit="Gym membership">
          Gym membership
        </div>
        <div class="benefit-chip" data-benefit="Sign-in Bonus">
          Sign-in Bonus
        </div>
        <div class="benefit-chip" data-benefit="Relocation Package">
          Relocation Package
        </div>
        <div class="benefit-chip" data-benefit="Company Vehicle">
          Company Vehicle
        </div>
        <div class="benefit-chip" data-benefit="Food Card">Food Card</div>
        <div class="benefit-chip" data-benefit="Snacks & Coffee">
          Snacks & Coffee
        </div>
        <div class="benefit-chip" data-benefit="Pet Friendly Office">
          Pet Friendly Office
        </div>
      </div>

      <label>Selected Benefits & Highlights</label>
      <div class="selected-benefits" id="selected-benefits">
        <div class="empty-message" id="empty-benefits-message">
          No benefits or highlights selected yet
        </div>
        <!-- Selected benefits will appear here -->
      </div>
    </div>

    <div class="next-btn-container">
      <button class="discard-btn mx-2">Discard</button>
      <button class="next-btn" id="next-btn">Next</button>
    </div>
  </div>
</div>

<style>
  :root {
    --primary: #4a6cf7;
    --primary-hover: #3859e9;
    --secondary: #f5f8ff;
    --text-color: #333;
    --border-color: #ddd;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  }

  body {
    background-color: #f9fafc;
    color: var(--text-color);
    line-height: 1.6;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  h1,
  h2 {
    margin-bottom: 20px;
    color: #252b42;
  }

  h1 {
    font-size: 28px;
  }

  h2 {
    font-size: 20px;
    font-weight: 600;
  }

  .form-container {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: var(--shadow);
  }

  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
  }

  .form-column {
    flex: 1;
    padding: 0 15px;
    min-width: 300px;
  }

  .form-group {
    margin-bottom: 24px;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 14px;
  }

  input,
  select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
  }

  input:focus,
  select:focus {
    outline: none;
    border-color: var(--primary);
  }

  .skills-container {
    margin-top: 20px;
  }

  .skills-input-group {
    display: flex;
    margin-bottom: 16px;
  }

  .skills-input-group input {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .add-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 0 20px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .add-btn:hover {
    background-color: var(--primary-hover);
  }

  .skill-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
  }

  .skill-chip {
    background-color: var(--secondary);
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
  }

  .skill-chip:hover {
    background-color: var(--primary);
    color: white;
  }

  .selected-skills {
    min-height: 100px;
    border: 1px dashed var(--border-color);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
    background-color: #fafafa;
  }

  .selected-skill {
    display: inline-flex;
    align-items: center;
    background-color: var(--primary);
    color: white;
    padding: 6px 12px;
    border-radius: 30px;
    margin: 0 8px 8px 0;
    font-size: 14px;
  }

  .remove-skill {
    margin-left: 8px;
    cursor: pointer;
    font-size: 16px;
  }

  .next-btn-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }

  /* Salary Container Styling */
  .salary-container {
    background-color: white;
    border-radius: 8px;
    margin-bottom: 30px;
  }

  .salary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .form-group {
    display: flex;
    flex-direction: column;
  }

  .form-group label {
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .form-group input,
  .form-group select {
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
  }

  .form-group input:focus,
  .form-group select:focus {
    outline: none;
    border-color: var(--primary);
  }

  .next-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .discard-btn {
    background-color: white;
    color: black;
    border: 1px solid rgb(100, 24, 34);
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  .discard-btn:hover {
    background-color: rgb(100, 24, 34);
    color: white;
  }

  .next-btn:hover {
    background-color: var(--primary-hover);
  }

  .empty-message {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 20px;
  }
  .error-message {
    color: #e63946;
    font-size: 12px;
    margin-top: 5px;
  }

  .input-error {
    border-color: #e63946 !important;
  }

  .no-data-message {
    background-color: #fff3cd;
    color: #856404;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 10px;
    font-size: 14px;
  }

  .no-data-message a {
    color: #0056b3;
    text-decoration: underline;
    font-weight: 600;
  }

  .no-data-message a:hover {
    text-decoration: none;
  }

  .selected-benefits {
    min-height: 100px;
    border: 1px dashed var(--border-color);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
    background-color: #fafafa;
  }

  .selected-benefit {
    display: inline-flex;
    align-items: center;
    background-color: rgb(44, 44, 44);
    color: white;
    padding: 6px 12px;
    border-radius: 30px;
    margin: 0 8px 8px 0;
    font-size: 14px;
  }

  .remove-benefit {
    margin-left: 8px;
    cursor: pointer;
    font-size: 16px;
  }

  .benefit-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
  }

  .benefit-chip {
    background-color: var(--secondary);
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
  }

  .benefit-chip:hover {
    background-color: rgb(44, 44, 44);
    color: white;
  }
</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const skillsInput = document.getElementById("skills-input");
    const addSkillBtn = document.getElementById("add-skill-btn");
    const selectedSkillsContainer = document.getElementById("selected-skills");
    const emptySkillsMessage = document.getElementById("empty-skills-message");
    const skillChips = document.querySelectorAll(".skill-chip");

    // Array to store selected skills
    let selectedSkills = [];

    // Function to add a skill
    function addSkill(skill) {
      if (!skill || selectedSkills.includes(skill)) return;

      selectedSkills.push(skill);
      updateSelectedSkillsDisplay();
      skillsInput.value = "";
    }

    // Function to remove a skill
    function removeSkill(skill) {
      selectedSkills = selectedSkills.filter((s) => s !== skill);
      updateSelectedSkillsDisplay();
    }

    // Function to update the selected skills display
    function updateSelectedSkillsDisplay() {
      if (selectedSkills.length === 0) {
        emptySkillsMessage.style.display = "block";
        selectedSkillsContainer.innerHTML = "";
        selectedSkillsContainer.appendChild(emptySkillsMessage);
        return;
      }

      emptySkillsMessage.style.display = "none";

      // Clear container first
      selectedSkillsContainer.innerHTML = "";

      // Add each skill as a chip
      selectedSkills.forEach((skill) => {
        const skillElement = document.createElement("div");
        skillElement.className = "selected-skill";
        skillElement.innerHTML = `
                    ${skill}
                    <span class="remove-skill" data-skill="${skill}">&times;</span>
                `;
        selectedSkillsContainer.appendChild(skillElement);
      });

      // Add event listeners to remove buttons
      document.querySelectorAll(".remove-skill").forEach((btn) => {
        btn.addEventListener("click", function () {
          removeSkill(this.getAttribute("data-skill"));
        });
      });
    }

    // Event listener for Add button
    addSkillBtn.addEventListener("click", function () {
      const skill = skillsInput.value.trim();
      addSkill(skill);
    });

    // Event listener for Enter key in input
    skillsInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        const skill = this.value.trim();
        addSkill(skill);
      }
    });

    // Event listeners for skill chips
    skillChips.forEach((chip) => {
      chip.addEventListener("click", function () {
        const skill = this.getAttribute("data-skill");
        addSkill(skill);
      });
    });

    const benefitsInput = document.getElementById("benefits-input");
    const addBenefitBtn = document.getElementById("add-benefit-btn");
    const selectedBenefitsContainer =
      document.getElementById("selected-benefits");
    const emptyBenefitsMessage = document.getElementById(
      "empty-benefits-message"
    );
    const benefitChips = document.querySelectorAll(".benefit-chip");

    // Array to store selected benefits
    let selectedBenefits = [];

    // Function to add a benefit
    function addBenefit(benefit) {
      if (!benefit || selectedBenefits.includes(benefit)) return;

      selectedBenefits.push(benefit);
      updateSelectedBenefitsDisplay();
      benefitsInput.value = "";
    }

    // Function to remove a benefit
    function removeBenefit(benefit) {
      selectedBenefits = selectedBenefits.filter((b) => b !== benefit);
      updateSelectedBenefitsDisplay();
    }

    // Function to update the selected benefits display
    function updateSelectedBenefitsDisplay() {
      if (selectedBenefits.length === 0) {
        emptyBenefitsMessage.style.display = "block";
        selectedBenefitsContainer.innerHTML = "";
        selectedBenefitsContainer.appendChild(emptyBenefitsMessage);
        return;
      }

      emptyBenefitsMessage.style.display = "none";

      // Clear container first
      selectedBenefitsContainer.innerHTML = "";

      // Add each benefit as a chip
      selectedBenefits.forEach((benefit) => {
        const benefitElement = document.createElement("div");
        benefitElement.className = "selected-benefit";
        benefitElement.innerHTML = `
                    ${benefit}
                    <span class="remove-benefit" data-benefit="${benefit}">&times;</span>
                `;
        selectedBenefitsContainer.appendChild(benefitElement);
      });

      // Add event listeners to remove buttons
      document.querySelectorAll(".remove-benefit").forEach((btn) => {
        btn.addEventListener("click", function () {
          removeBenefit(this.getAttribute("data-benefit"));
        });
      });
    }

    // Event listener for Add button
    addBenefitBtn.addEventListener("click", function () {
      const benefit = benefitsInput.value.trim();
      addBenefit(benefit);
    });

    // Event listener for Enter key in input
    benefitsInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        const benefit = this.value.trim();
        addBenefit(benefit);
      }
    });

    // Event listeners for benefit chips
    benefitChips.forEach((chip) => {
      chip.addEventListener("click", function () {
        const benefit = this.getAttribute("data-benefit");
        addBenefit(benefit);
      });
    });

    // Function to display validation error messages
    function showError(inputElement, message) {
      // Remove any existing error message
      const existingError =
        inputElement.parentElement.querySelector(".error-message");
      if (existingError) existingError.remove();

      // Create and add error message
      const errorElement = document.createElement("div");
      errorElement.className = "error-message";
      errorElement.textContent = message;
      inputElement.parentElement.appendChild(errorElement);

      // Highlight the input field
      inputElement.classList.add("input-error");
    }

    // Function to clear validation error
    function clearError(inputElement) {
      const errorElement =
        inputElement.parentElement.querySelector(".error-message");
      if (errorElement) errorElement.remove();
      inputElement.classList.remove("input-error");
    }

    // Function to validate the form
    function validateForm() {
      let isValid = true;

      // Validate Position Title
      const positionTitle = document.getElementById("position-title");
      if (!positionTitle.value.trim()) {
        showError(positionTitle, "Role Title is required");
        isValid = false;
      } else {
        clearError(positionTitle);
      }

      // Validate Office Location
      const officeLocation = document.getElementById("office-location");
      if (!officeLocation.disabled && !officeLocation.value) {
        showError(officeLocation, "Office Location is required");
        isValid = false;
      } else if (officeLocation.disabled) {
        showError(
          officeLocation,
          "Please add office locations in your preferences first"
        );
        isValid = false;
      } else {
        clearError(officeLocation);
      }

      // Validate Work Schedule
      const workSchedule = document.getElementById("work-schedule");
      if (!workSchedule.disabled && !workSchedule.value) {
        showError(workSchedule, "Work Schedule is required");
        isValid = false;
      } else if (workSchedule.disabled) {
        showError(
          workSchedule,
          "Please add work schedules in your preferences first"
        );
        isValid = false;
      } else {
        clearError(workSchedule);
      }

      // Validate Office Schedule
      const officeSchedule = document.getElementById("office-schedule");
      if (!officeSchedule.disabled && !officeSchedule.value) {
        showError(officeSchedule, "Office Schedule is required");
        isValid = false;
      } else if (officeSchedule.disabled) {
        showError(
          officeSchedule,
          "Please add office schedules in your preferences first"
        );
        isValid = false;
      } else {
        clearError(officeSchedule);
      }

      return isValid;
    }

    // Event listener for Next button
    document.getElementById("next-btn").addEventListener("click", function () {
      // Validate the form first
      if (!validateForm()) {
        return; // Stop if validation fails
      }

      // Collect all data
      const formData = {
        roleTitle: document.getElementById("position-title").value,
        officeLocation: document.getElementById("office-location").value,
        workSchedule: document.getElementById("work-schedule").value,
        officeSchedule: document.getElementById("office-schedule").value,
        skills: selectedSkills,
        salaryMin: document.getElementById("salary-min").value || 0,
        salaryMax: document.getElementById("salary-max").value || 0,
        salaryCurrency: document.getElementById("salary-currency").value || "",
        benefits: selectedBenefits,
      };

      sessionStorage.setItem("jobFormData", JSON.stringify(formData));

      //console.log('Form Data stored in sessionStorage:', formData);
      window.location.href = "{% url 'description' %}";

      // Navigate to the next page
      // window.location.href = 'next-page.html';
    });

    // Add input event listeners to clear errors when user starts typing
    document
      .getElementById("position-title")
      .addEventListener("input", function () {
        clearError(this);
      });

    document
      .getElementById("office-location")
      .addEventListener("input", function () {
        clearError(this);
      });

    document
      .getElementById("work-schedule")
      .addEventListener("change", function () {
        clearError(this);
      });

    document
      .getElementById("office-schedule")
      .addEventListener("change", function () {
        clearError(this);
      });
  });
</script>
{% endblock %}

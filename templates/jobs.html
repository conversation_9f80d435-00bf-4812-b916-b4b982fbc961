{% extends 'main.html' %}
{% block content %}

<div class="projects-dashboard">
  <!-- Header Section -->
  <div class="dashboard-header">
    <h1>Job Listings</h1>
  </div>

  <!-- Stats Cards -->
  <div class="stats-container">
    <div class="stat-card">
      <div class="stat-icon active-icon">
        <i class="fas fa-briefcase"></i>
      </div>
      <div class="stat-info">
        <h3>Active Jobs</h3>
        <p class="stat-number">{{ active_jobs_count }}</p>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-icon applicants-icon">
        <i class="fas fa-users"></i>
      </div>
      <div class="stat-info">
        <h3>Total Applicants</h3>
        <p class="stat-number">{{ total_applicants_count }}</p>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-icon interviews-icon">
        <i class="bi bi-archive-fill"></i>
      </div>
      <div class="stat-info">
        <h3>Archived Jobs</h3>
        <p class="stat-number">{{ archived_jobs }}</p>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-icon hired-icon">
        <i class="fas fa-user-check"></i>
      </div>
      <div class="stat-info">
        <h3>Candidates Hired</h3>
        <p class="stat-number">{{ candidates_hired }}</p>
      </div>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="filter-section">
    <form id="filter-form" method="GET" action="{% url 'jobs' %}">
      <div class="filter-container">
        <div class="filter-group">
          <label for="department-filter">Department</label>
          <select id="department-filter" name="department" class="filter-dropdown" onchange="this.form.submit()">
            <option value="">All Departments</option>
            {% for department in all_departments %}
            <option value="{{ department }}" {% if department_filter == department %}selected{% endif %}>{{ department }}</option>
            {% endfor %}
          </select>
        </div>
        
        <div class="filter-group">
          <label for="status-filter">Status</label>
          <select id="status-filter" name="status" class="filter-dropdown" onchange="this.form.submit()">
            <option value="">All Statuses</option>
            {% for status in all_statuses %}
            <option value="{{ status }}" {% if status_filter == status %}selected{% endif %}>{{ status }}</option>
            {% endfor %}
          </select>
        </div>
        
        <div class="filter-group">
          <label for="location-filter">Location</label>
          <select id="location-filter" name="location" class="filter-dropdown" onchange="this.form.submit()">
            <option value="">All Locations</option>
            {% for location in all_locations %}
            <option value="{{ location }}" {% if location_filter == location %}selected{% endif %}>{{ location }}</option>
            {% endfor %}
          </select>
        </div>
        
        <div class="filter-group">
          <label for="date-filter">Posted Date</label>
          <select id="date-filter" name="date" class="filter-dropdown" onchange="this.form.submit()">
            <option value="">All Time</option>
            <option value="today" {% if date_filter == 'today' %}selected{% endif %}>Today</option>
            <option value="this-week" {% if date_filter == 'this-week' %}selected{% endif %}>This Week</option>
            <option value="this-month" {% if date_filter == 'this-month' %}selected{% endif %}>This Month</option>
            <option value="last-month" {% if date_filter == 'last-month' %}selected{% endif %}>Last Month</option>
          </select>
        </div>
      </div>
    </form>
    
    <div class="active-filters">
      {% for filter_type, filter_value in active_filters %}
      <span class="filter-tag" data-filter-type="{{ filter_type }}">{{ filter_value }} <i class="fas fa-times"></i></span>
      {% endfor %}
      {% if active_filters %}
      <button class="clear-filters">Clear all filters</button>
      {% endif %}
    </div>
  </div>

  <!-- Projects Grid -->
  <div class="projects-grid">
    <!-- Job Cards -->
    {% for vacancy in vacancies %}
    <div class="job-card">
      <div class="job-header">
        <div class="job-department">{{ vacancy.vacancy_bus_unit }}</div>
        <div class="job-status {% if vacancy.vacancy_status == 'Active' %}job-status-active{% else %}job-status-closed{% endif %}">{{ vacancy.vacancy_status }}</div>
      </div>
      
      <h2 class="job-title">{{ vacancy.vacancy_title }}</h2>
      <p class="job-location"><i class="fas fa-map-marker-alt"></i> {{ vacancy.vacancy_city }}, {{ vacancy.vacancy_country }}</p>
      
      <div class="job-stats">
        <div class="job-stat">
          <i class="fas fa-users"></i>
          <span class="job-stat-value">{{ vacancy.number_of_applicants_temp }}</span>
          <span class="job-stat-label">Applicants</span>
        </div>
        
        <div class="job-stat">
          <i class="fas fa-calendar-check"></i>
          <span class="job-stat-value">{{ vacancy.interview_count|default:"0" }}</span>
          <span class="job-stat-label">Interviews</span>
        </div>
        
        <div class="job-stat">
          {% if vacancy.vacancy_status == 'Closed' %}
          <i class="fas fa-user-check"></i>
          <span class="job-stat-value">{{ vacancy.hired_count|default:"0" }}</span>
          <span class="job-stat-label">Hired</span>
          {% else %}
          <i class="fas fa-hourglass-half"></i>
          <span class="job-stat-value">{{ vacancy.days_open }}</span>
          <span class="job-stat-label">Days Open</span>
          {% endif %}
        </div>
      </div>
      
      <div class="job-footer">
        <p class="job-date">{% if vacancy.vacancy_status == 'Closed' %}Closed on:{% else %}Posted on:{% endif %} {{ vacancy.vacancy_creation_date|date:"M d, Y" }}</p>
        <a href="{% url 'details_with_id' vacancy.vacancy_id %}" class="job-action-btn">View Details</a>
      </div>
    </div>
    {% endfor %}

  </div>

  <!-- Pagination -->
  <div class="pagination-container">
    <div class="pagination-info">
      Showing <span class="current-range">1-6</span> of <span class="total-count">12</span> jobs
    </div>
    <div class="pagination-controls">
      <button class="pagination-btn" disabled>
        <i class="fas fa-chevron-left"></i>
      </button>
      <button class="pagination-btn pagination-active">1</button>
      <button class="pagination-btn">2</button>
      <button class="pagination-btn">
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
  </div>
</div>

<!-- CSS Styles -->
<style>
  /* Main Dashboard Styles */
  .projects-dashboard {
    padding: 2rem;
    background-color: #f8f9fa;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: #333;
    min-height: 100vh;
  }

  /* Header Styles */
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .dashboard-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #1a202c;
  }

  .header-actions {
    display: flex;
    gap: 1rem;
  }

  .search-container {
    position: relative;
    width: 300px;
  }

  .search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background-color: white;
    font-size: 0.875rem;
    outline: none;
    transition: all 0.2s;
  }

  .search-input:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  }

  .search-btn {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #94a3b8;
    cursor: pointer;
  }

  .btn-primary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background-color: #4f46e5;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .btn-primary:hover {
    background-color: #4338ca;
  }

  /* Stats Cards */
  .stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
  }

  .active-icon {
    background-color: #4f46e5;
  }

  .applicants-icon {
    background-color: #0891b2;
  }

  .interviews-icon {
    background-color: #ca8a04;
  }

  .hired-icon {
    background-color: #16a34a;
  }

  .stat-info h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
    margin: 0 0 0.25rem 0;
  }

  .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0;
  }

  /* Filter Section Styles */
  .filter-section {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1rem;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
  }

  .filter-group label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #64748b;
    margin-bottom: 0.375rem;
    text-transform: uppercase;
  }

  .filter-dropdown {
    padding: 0.625rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.875rem;
    color: #1a202c;
    outline: none;
    transition: all 0.2s;
  }

  .filter-dropdown:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  }

  .active-filters {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .filter-tag {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    background-color: #f1f5f9;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    color: #475569;
  }

  .filter-tag i {
    cursor: pointer;
    color: #94a3b8;
    transition: color 0.2s;
  }

  .filter-tag i:hover {
    color: #475569;
  }

  .clear-filters {
    border: none;
    background: none;
    color: #4f46e5;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    transition: color 0.2s;
  }

  .clear-filters:hover {
    color: #4338ca;
    text-decoration: underline;
  }

  /* Job Cards Grid */
  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .job-card {
    background-color: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    flex-direction: column;
  }

  .job-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
  }

  .job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
  }

  .job-department {
    font-size: 0.75rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
  }

  .job-status {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
  }

  .job-status-active {
    background-color: #ecfdf5;
    color: #059669;
  }

  .job-status-closed {
    background-color: #f1f5f9;
    color: #64748b;
  }

  .job-title {
    padding: 1rem 1.5rem 0.5rem;
    margin: 0;
    font-size: 1.125rem;
    font-weight: 700;
    color: #1a202c;
  }

  .job-location {
    padding: 0 1.5rem;
    margin: 0 0 1rem 0;
    font-size: 0.875rem;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .job-stats {
    display: flex;
    justify-content: space-between;
    padding: 0 1.5rem 1rem;
    margin-top: auto;
  }

  .job-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .job-stat i {
    font-size: 1rem;
    color: #4f46e5;
    margin-bottom: 0.25rem;
  }

  .job-stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: #1a202c;
  }

  .job-stat-label {
    font-size: 0.75rem;
    color: #64748b;
  }

  .job-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #f1f5f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .job-date {
    font-size: 0.75rem;
    color: #64748b;
    margin: 0;
  }

  .job-action-btn {
    padding: 0.5rem 1rem;
    background-color: #4f46e5;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: background-color 0.2s;
  }

  .job-action-btn:hover {
    text-decoration: none;
    color: white;
    background-color: #4338ca;
  }

  /* Pagination Styles */
  .pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
  }

  .pagination-info {
    font-size: 0.875rem;
    color: #64748b;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .pagination-btn {
    min-width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e2e8f0;
    background-color: white;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #475569;
    cursor: pointer;
    transition: all 0.2s;
  }

  .pagination-btn:hover:not(:disabled) {
    background-color: #f8fafc;
    border-color: #cbd5e1;
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .pagination-active {
    background-color: #4f46e5;
    border-color: #4f46e5;
    color: white;
  }

  .pagination-active:hover {
    background-color: #4338ca !important;
    border-color: #4338ca !important;
  }

  /* Responsive Adjustments */
  @media (max-width: 1200px) {
    .filter-group {
      min-width: 160px;
    }

    .projects-grid {
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
  }

  @media (max-width: 992px) {
    .projects-dashboard {
      padding: 1.5rem;
    }
    
    .header-actions {
      flex-direction: column;
      gap: 0.75rem;
    }
    
    .search-container {
      width: 100%;
    }
    
    .pagination-container {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .pagination-info, .pagination-controls {
      width: 100%;
      justify-content: center;
    }
  }

  @media (max-width: 768px) {
    .filter-container {
      flex-direction: column;
      gap: 1rem;
    }
    
    .filter-group {
      width: 100%;
    }

    .projects-grid {
      grid-template-columns: 1fr;
    }
  }
</style>

<!-- JavaScript Functionality -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Individual filter tag removal
    document.querySelectorAll('.filter-tag i').forEach(icon => {
      icon.addEventListener('click', function() {
        const filterTag = this.parentElement;
        const filterType = filterTag.dataset.filterType;
        const form = document.getElementById('filter-form');
        
        // Get the correct input element
        let inputName = filterType;
        // Special case for date filter (name="date" in HTML)
        if (filterType === 'date_filter') inputName = 'date'; // Match the select name
        
        const input = form.querySelector(`[name="${inputName}"]`);
        if (input) {
          input.value = ''; // Reset the select/dropdown
          form.submit(); // Explicitly submit the form
        }
      });
    });
    
    // Clear ALL filters
    const clearFiltersBtn = document.querySelector('.clear-filters');
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', function(e) {
        e.preventDefault();
        const form = document.getElementById('filter-form');
        
        // 1. Reset all select elements
        form.querySelectorAll('select').forEach(select => {
          select.value = '';
        });
        
        // 2. Remove hidden search parameter if exists
        const hiddenSearchInput = form.querySelector('input[name="search"]');
        if (hiddenSearchInput) hiddenSearchInput.remove();
        
        // 3. Clear visible search input
        const searchInput = document.querySelector('.search-input');
        if (searchInput) searchInput.value = '';
        
        // 4. Submit the form instead of redirecting
        form.submit();
      });
    }
    function setupDynamicPagination() {
      const totalJobs = {{ total_jobs }};
      const jobsPerPage = {{ jobs_per_page }};
      const currentPage = {{ page_number }};
      const totalPages = {{ total_pages }};
      
      // Update pagination info text
      const paginationInfo = document.querySelector('.pagination-info');
      if (paginationInfo) {
        paginationInfo.innerHTML = `Showing <span class="current-range">{{ start_index }}-{{ end_index }}</span> of <span class="total-count">${totalJobs}</span> jobs`;
      }
      
      // Update pagination controls
      const paginationControls = document.querySelector('.pagination-controls');
      if (paginationControls && totalPages > 0) {
        // Clear existing pagination buttons
        paginationControls.innerHTML = '';
        
        // Previous page button
        const prevBtn = document.createElement('button');
        prevBtn.className = 'pagination-btn';
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        if (currentPage <= 1) {
          prevBtn.disabled = true;
        } else {
          prevBtn.addEventListener('click', function() {
            navigateToPage(currentPage - 1);
          });
        }
        paginationControls.appendChild(prevBtn);
        
        // Page number buttons - show max 5 pages with current page in the middle if possible
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        // Adjust start page if we're near the end
        if (endPage - startPage + 1 < maxVisiblePages) {
          startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
        
        for (let i = startPage; i <= endPage; i++) {
          const pageBtn = document.createElement('button');
          pageBtn.className = 'pagination-btn';
          if (i === currentPage) {
            pageBtn.classList.add('pagination-active');
          }
          pageBtn.textContent = i;
          
          pageBtn.addEventListener('click', function() {
            navigateToPage(i);
          });
          
          paginationControls.appendChild(pageBtn);
        }
        
        // Next page button
        const nextBtn = document.createElement('button');
        nextBtn.className = 'pagination-btn';
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        if (currentPage >= totalPages) {
          nextBtn.disabled = true;
        } else {
          nextBtn.addEventListener('click', function() {
            navigateToPage(currentPage + 1);
          });
        }
        paginationControls.appendChild(nextBtn);
        
        // Hide pagination if there's only one page
        if (totalPages <= 1) {
          const paginationContainer = document.querySelector('.pagination-container');
          if (paginationContainer) {
            paginationContainer.style.display = 'none';
          }
        }
      }
    }
    
    // Function to navigate to a specific page
    function navigateToPage(pageNumber) {
      const form = document.getElementById('filter-form');
      
      // Check if page input already exists
      let pageInput = form.querySelector('input[name="page"]');
      if (!pageInput) {
        pageInput = document.createElement('input');
        pageInput.type = 'hidden';
        pageInput.name = 'page';
        form.appendChild(pageInput);
      }
      
      pageInput.value = pageNumber;
      form.submit();
    }
    
    // Initialize dynamic pagination
    setupDynamicPagination();
  });
</script>
<script>
  
  document.addEventListener('DOMContentLoaded', function() {
    
    // Status filter functionality (remaining code stays the same)
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
      statusFilter.addEventListener('change', function() {
        const value = this.value;
        if (value) {
          // Add filter tag if not already present
          const statusTagExists = Array.from(document.querySelectorAll('.filter-tag')).some(
            tag => tag.textContent.toLowerCase().includes(value.toLowerCase())
          );
          
          if (!statusTagExists) {
            const activeFilters = document.querySelector('.active-filters');
            const selectedOption = statusFilter.options[statusFilter.selectedIndex];
            
            const newTag = document.createElement('span');
            newTag.className = 'filter-tag';
            newTag.dataset.filterType = 'status';
            newTag.innerHTML = `${selectedOption.text} <i class="fas fa-times"></i>`;
            
            // Add click event to remove tag
            newTag.querySelector('i').addEventListener('click', function() {
              statusFilter.value = '';
              document.getElementById('filter-form').submit();
            });
            
            activeFilters.insertBefore(newTag, document.querySelector('.clear-filters'));
          }
        }
      });
    }
    
    // Department filter functionality 
    const departmentFilter = document.getElementById('department-filter');
    if (departmentFilter) {
      departmentFilter.addEventListener('change', function() {
        const value = this.value;
        if (value) {
          // Add filter tag if not already present
          const deptTagExists = Array.from(document.querySelectorAll('.filter-tag')).some(
            tag => tag.textContent.toLowerCase().includes(value.toLowerCase())
          );
          
          if (!deptTagExists) {
            const activeFilters = document.querySelector('.active-filters');
            const selectedOption = departmentFilter.options[departmentFilter.selectedIndex];
            
            const newTag = document.createElement('span');
            newTag.className = 'filter-tag';
            newTag.dataset.filterType = 'department';
            newTag.innerHTML = `${selectedOption.text} <i class="fas fa-times"></i>`;
            
            // Add click event to remove tag
            newTag.querySelector('i').addEventListener('click', function() {
              departmentFilter.value = '';
              document.getElementById('filter-form').submit();
            });
            
            activeFilters.insertBefore(newTag, document.querySelector('.clear-filters'));
          }
        }
      });
    }
    
    // Location filter functionality
    const locationFilter = document.getElementById('location-filter');
    if (locationFilter) {
      locationFilter.addEventListener('change', function() {
        const value = this.value;
        if (value) {
          // Add filter tag if not already present
          const locationTagExists = Array.from(document.querySelectorAll('.filter-tag')).some(
            tag => tag.textContent.toLowerCase().includes(locationFilter.options[locationFilter.selectedIndex].text.toLowerCase())
          );
          
          if (!locationTagExists) {
            const activeFilters = document.querySelector('.active-filters');
            const selectedOption = locationFilter.options[locationFilter.selectedIndex];
            
            const newTag = document.createElement('span');
            newTag.className = 'filter-tag';
            newTag.dataset.filterType = 'location';
            newTag.innerHTML = `${selectedOption.text} <i class="fas fa-times"></i>`;
            
            // Add click event to remove tag
            newTag.querySelector('i').addEventListener('click', function() {
              locationFilter.value = '';
              document.getElementById('filter-form').submit();
            });
            
            activeFilters.insertBefore(newTag, document.querySelector('.clear-filters'));
          }
        }
      });
    }
    
    // Date filter functionality (adding this as it wasn't in the original)
    const dateFilter = document.getElementById('date-filter');
    if (dateFilter) {
      dateFilter.addEventListener('change', function() {
        const value = this.value;
        if (value) {
          // Add filter tag if not already present
          const dateTagExists = Array.from(document.querySelectorAll('.filter-tag')).some(
            tag => tag.textContent.toLowerCase().includes(dateFilter.options[dateFilter.selectedIndex].text.toLowerCase())
          );
          
          if (!dateTagExists) {
            const activeFilters = document.querySelector('.active-filters');
            const selectedOption = dateFilter.options[dateFilter.selectedIndex];
            
            const newTag = document.createElement('span');
            newTag.className = 'filter-tag';
            newTag.dataset.filterType = 'date';
            newTag.innerHTML = `${selectedOption.text} <i class="fas fa-times"></i>`;
            
            // Add click event to remove tag
            newTag.querySelector('i').addEventListener('click', function() {
              dateFilter.value = '';
              document.getElementById('filter-form').submit();
            });
            
            activeFilters.insertBefore(newTag, document.querySelector('.clear-filters'));
          }
        }
      });
    }
    
    // Job card hover effect for action button
    const jobCards = document.querySelectorAll('.job-card');
    jobCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        const actionBtn = this.querySelector('.job-action-btn');
        if (actionBtn) {
          actionBtn.style.transform = 'translateY(-2px)';
        }
      });
      
      card.addEventListener('mouseleave', function() {
        const actionBtn = this.querySelector('.job-action-btn');
        if (actionBtn) {
          actionBtn.style.transform = 'translateY(0)';
        }
      });
    });

  });
{% endblock content %}
{% load static %}
{% load i18n %}
<!-- Font Awesome for icons -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">


<nav class="navbar">
    <!-- Logo/Company Name -->
    <a href="{% url 'feed' %}" class="navbar-brand">

        <span class="brand-text">Canvider</span>
    </a>

    <!-- Mobile Menu Toggle -->
    <button class="mobile-toggle" id="mobileToggle">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Nav Links -->
    <div class="navbar-links" id="navLinks">
        <div class="nav-item">
            <a href="{% url 'feed' %}" class="nav-link">
                <i class="fas fa-stream"></i> {% trans "Feed" %}
            </a>
        </div>
        <div class="nav-item">
            <a href="{% url 'jobs' %}" class="nav-link">
                <i class="fas fa-briefcase"></i> {% trans "Jobs" %}
            </a>
        </div>
        <div class="nav-item">
            <a href="{% url 'people' %}" class="nav-link">
                <i class="fas fa-users"></i> {% trans "Applicants" %}
            </a>
        </div>
    </div>

    <!-- Right side (create, notifications, profile) -->
    <div class="navbar-right">
        <a href="{% url 'create' %}" class="create-btn">
            <i class="fas fa-plus"></i> {% trans "Create Job" %}
        </a>
        <!-- Notification is not doable for now
        <div class="nav-item">
            <button class="icon-btn" id="notificationBtn">
                <i class="fas fa-bell"></i>
                <span class="notification-dot"></span>
            </button>

            <div class="notification-dropdown" id="notificationDropdown">
                <div class="notification-header">
                    <h3>Notifications</h3>
                    <button>Mark all as read</button>
                </div>
                <div class="notification-list">
                    <div class="notification-item unread">
                        <div class="notification-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="notification-content">
                            <p class="notification-text">John Doe applied for <strong>Senior Developer</strong> position.</p>
                            <p class="notification-time">2 hours ago</p>
                        </div>
                    </div>
                    <div class="notification-item">
                        <div class="notification-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="notification-content">
                            <p class="notification-text">Your job posting for <strong>UI/UX Designer</strong> is expiring in 3 days.</p>
                            <p class="notification-time">1 day ago</p>
                        </div>
                    </div>
                    <div class="notification-item unread">
                        <div class="notification-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="notification-content">
                            <p class="notification-text">Your job for <strong>Marketing Specialist</strong> reached 100 views.</p>
                            <p class="notification-time">2 days ago</p>
                        </div>
                    </div>
                </div>
                <a href="#" class="view-all-btn">View all notifications</a>
            </div>
        </div>
        -->

        <div class="nav-item">
            {% if request.user.is_authenticated and request.user.employee.profile_photo %}
                <img src="{{ request.user.employee.profile_photo }}" alt="{{ request.user.first_name }}'s Profile" class="avatar" id="profileBtn">
            {% else %}
                <div class="avatar-initials" id="profileBtn">
                    {% if request.user.is_authenticated %}
                        {{ request.user.first_name|first }}{{ request.user.last_name|first }}
                    {% else %}
                        <i class="fas fa-user"></i>
                    {% endif %}
                </div>
            {% endif %}

            <!-- Profile Dropdown -->
            <div class="dropdown" id="profileDropdown">
                <div class="dropdown-header">
                    {% if request.user.is_authenticated and request.user.employee.profile_photo %}
                        <img src="{{ request.user.employee.profile_photo }}" alt="{{ request.user.first_name }}'s Profile" class="avatar">
                    {% else %}
                        <div class="avatar-initials">
                            {% if request.user.is_authenticated %}
                                {{ request.user.first_name|first }}{{ request.user.last_name|first }}
                            {% else %}
                                <i class="fas fa-user"></i>
                            {% endif %}
                        </div>
                    {% endif %}
                    <div class="dropdown-header-info">
                        <span class="dropdown-header-name">
                            {% if request.user.is_authenticated %}
                                {{ request.user.first_name }} {{ request.user.last_name }}
                            {% else %}
                                Guest User
                            {% endif %}
                        </span>
                        <span class="dropdown-header-title">
                            {% if request.user.is_authenticated and request.user.employee %}
                                {{ request.user.employee.role }}
                            {% else %}
                                Not logged in
                            {% endif %}
                        </span>
                    </div>
                </div>
                <a href="{% url 'profile' %}" class="dropdown-item">
                    <i class="fas fa-user"></i> {% trans "Profile" %}
                </a>
                <a href="{% url 'settings' %}" class="dropdown-item">
                    <i class="fas fa-cog"></i> {% trans "Settings" %}
                </a>
                <div class="dropdown-divider"></div>
                <a href="{% url 'signin' %}" class="dropdown-item logout-item">
                    <i class="fas fa-sign-out-alt"></i> {% trans "Logout" %}
                </a>
            </div>
        </div>
    </div>
</nav>
<style>
    :root {
        --primary: #4361ee;
        --primary-light: #5171ff;
        --secondary: #3a0ca3;
        --light: #f8f9fa;
        --dark: #212529;
        --success: #4cc9f0;
        --gray: #6c757d;
        --gray-light: #e9ecef;
        --transition: all 0.3s ease;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .navbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem 2rem;
        background-color: white;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
        position: sticky;
        top: 0;
        z-index: 1000;
        height: 70px;
    }

    .navbar-brand {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary);
        text-decoration: none;
        display: flex;
        align-items: center;
    }

    .brand-text {
        margin-left: 10px;
    }

    .navbar-brand img {
        height: 40px;
    }

    .navbar-links {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .nav-item {
        position: relative;
    }

    .nav-link {
        color: var(--dark);
        text-decoration: none;
        padding: 0.8rem 1rem;
        border-radius: 8px;
        font-weight: 500;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .nav-link:hover {
        color: var(--primary);
        background-color: rgba(67, 97, 238, 0.05);
    }

    .nav-link.active {
        color: var(--primary);
        background-color: rgba(67, 97, 238, 0.1);
    }

    .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 8px;
        height: 8px;
        background-color: var(--primary);
        border-radius: 50%;
    }

    .navbar-right {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .create-btn {
        background-color: var(--primary);
        color: white;
        padding: 0.6rem 1.2rem;
        border-radius: 8px;
        border: none;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .create-btn:hover {
        background-color: var(--primary-light);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(67, 97, 238, 0.15);
        color: white;
        text-decoration: none;
    }

    .icon-btn {
        background: none;
        border: none;
        color: var(--gray);
        font-size: 1.2rem;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
        position: relative;
    }

    .icon-btn:hover {
        background-color: var(--gray-light);
        color: var(--dark);
    }

    .notification-dot {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #f72585;
    }

    .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        cursor: pointer;
        border: 2px solid transparent;
        transition: var(--transition);
    }

    .avatar:hover {
        border-color: var(--primary-light);
    }

    .avatar-initials {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--primary);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        cursor: pointer;
        border: 2px solid transparent;
        transition: var(--transition);
    }

    .avatar-initials:hover {
        border-color: var(--primary-light);
        background-color: var(--primary-light);
    }

    .dropdown {
        position: absolute;
        top: 60px;
        right: 0;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        width: 220px;
        padding: 1rem;
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px);
        transition: var(--transition);
        z-index: 1001;
    }

    .dropdown.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .dropdown-header {
        display: flex;
        align-items: center;
        gap: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--gray-light);
        margin-bottom: 10px;
    }

    .dropdown-header-info {
        display: flex;
        flex-direction: column;
    }

    .dropdown-header-name {
        font-weight: 600;
        font-size: 0.9rem;
    }

    .dropdown-header-title {
        font-size: 0.8rem;
        color: var(--gray);
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
        text-decoration: none;
        color: var(--dark);
        border-radius: 8px;
        transition: var(--transition);
    }

    .dropdown-item:hover {
        background-color: var(--gray-light);
    }

    .dropdown-item i {
        font-size: 1rem;
        color: var(--gray);
        width: 20px;
        text-align: center;
    }

    .dropdown-divider {
        height: 1px;
        background-color: var(--gray-light);
        margin: 8px 0;
    }

    .logout-item {
        color: #e63946;
    }

    .logout-item i {
        color: #e63946;
    }

    /* Notification dropdown */
    .notification-dropdown {
        position: absolute;
        top: 60px;
        right: 0;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        width: 300px;
        padding: 1rem;
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px);
        transition: var(--transition);
        z-index: 1001;
    }

    .notification-dropdown.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .notification-header h3 {
        font-size: 1rem;
        font-weight: 600;
    }

    .notification-header button {
        background: none;
        border: none;
        color: var(--primary);
        font-size: 0.8rem;
        cursor: pointer;
    }

    .notification-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .notification-item {
        padding: 10px;
        border-radius: 8px;
        display: flex;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 8px;
        transition: var(--transition);
    }

    .notification-item:hover {
        background-color: var(--gray-light);
    }

    .notification-item.unread {
        background-color: rgba(67, 97, 238, 0.05);
    }

    .notification-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: rgba(67, 97, 238, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
        flex-shrink: 0;
    }

    .notification-content {
        flex-grow: 1;
    }

    .notification-text {
        font-size: 0.85rem;
        margin-bottom: 4px;
    }

    .notification-time {
        font-size: 0.75rem;
        color: var(--gray);
    }

    .view-all-btn {
        display: block;
        text-align: center;
        padding: 10px;
        background-color: var(--gray-light);
        color: var(--dark);
        border-radius: 8px;
        margin-top: 10px;
        text-decoration: none;
        transition: var(--transition);
    }

    .view-all-btn:hover {
        background-color: var(--gray);
        color: white;
    }

    /* Mobile responsive */
    .mobile-toggle {
        display: none;
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--dark);
    }

    @media screen and (max-width: 991px) {
        .mobile-toggle {
            display: block;
        }

        .navbar-links {
            position: fixed;
            top: 70px;
            left: 0;
            right: 0;
            flex-direction: column;
            background-color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            transform: translateY(-100%);
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            align-items: flex-start;
            gap: 0;
        }

        .navbar-links.active {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }

        .nav-link {
            width: 100%;
        }

        .nav-link.active::after {
            display: none;
        }

        .dropdown, .notification-dropdown {
            position: fixed;
            top: auto;
            width: 100%;
            max-width: 300px;
        }
    }
</style>
<script>
    // Toggle mobile menu
    document.getElementById('mobileToggle').addEventListener('click', function() {
        document.getElementById('navLinks').classList.toggle('active');
    });

    // Profile dropdown
    document.getElementById('profileBtn').addEventListener('click', function(e) {
        document.getElementById('profileDropdown').classList.toggle('show');
        // Close notification dropdown if open
    //    document.getElementById('notificationDropdown').classList.remove('show');
        e.stopPropagation();
    });

    // Notification dropdown
    //document.getElementById('notificationBtn').addEventListener('click', function(e) {
      //  document.getElementById('notificationDropdown').classList.toggle('show');
        // Close profile dropdown if open
        //document.getElementById('profileDropdown').classList.remove('show');
        //e.stopPropagation();
    //});

    // Close dropdowns when clicking elsewhere
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.nav-item')) {
            document.getElementById('profileDropdown').classList.remove('show');
      //      document.getElementById('notificationDropdown').classList.remove('show');
        }
    });

    // Nav links active state
    function setActiveNavLink() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');

        // Remove active class from all links
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to the matching link
        navLinks.forEach(link => {
            const linkPath = link.getAttribute('href');
            // Remove Django template tags for comparison
            const cleanLinkPath = linkPath.replace(/\{% url '(.+)' %\}/g, '/$1');

            if (currentPath === cleanLinkPath ||
                (currentPath.includes(cleanLinkPath) && cleanLinkPath !== '/') ||
                (cleanLinkPath.includes(currentPath) && currentPath !== '/')) {
                link.classList.add('active');
            }
        });
    }
    // Run when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', setActiveNavLink);

    // Nav links click event (for single-page applications)
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
</script>
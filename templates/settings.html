{% extends 'main.html' %}

{% load static %}
{% load i18n %}
{% block content %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<div class="container">
    <div class="settings-header">
        <h1>{% trans "Settings" %}</h1>
        <p class="settings-subtitle">{% trans "Configure your recruitment workflow and manage your ATS settings" %}</p>
    </div>

    <div class="settings-grid">
        <!-- Preferences Card -->
        <div class="settings-card">
            <div class="settings-card-icon">
                <i class="fas fa-sliders-h"></i>
            </div>
            <div class="settings-card-content">
                <h2>Preferences</h2>
                <p>Configure default options for job creation including work schedules, office locations, and role titles.</p>
                <ul class="settings-features">
                    <li><i class="fas fa-check"></i> Define company work schedules</li>
                    <li><i class="fas fa-check"></i> Set up office locations</li>
                    <li><i class="fas fa-check"></i> Standardize role titles</li>
                    <li><i class="fas fa-check"></i> Configure office schedule options</li>
                </ul>
                <a href="{% url 'preferences' %}" class="settings-card-button">
                    Manage Preferences
                    <i class="fas fa-chevron-right"></i>
                </a>
            </div>
        </div>

        <!-- Templates Card -->
        <div class="settings-card">
            <div class="settings-card-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="settings-card-content">
                <h2>Templates</h2>
                <p>Create, edit, and manage job description templates to streamline your job posting process.</p>
                <ul class="settings-features">
                    <li><i class="fas fa-check"></i> Build reusable job templates</li>
                    <li><i class="fas fa-check"></i> Save time on repetitive descriptions</li>
                    <li><i class="fas fa-check"></i> Maintain consistent job postings</li>
                    <li><i class="fas fa-check"></i> Organize templates by department</li>
                </ul>
                <a href="{% url 'templates' %}" class="settings-card-button">
                    Manage Templates
                    <i class="fas fa-chevron-right"></i>
                </a>
            </div>
        </div>

        <!-- Invitations Card -->
        <div class="settings-card">
            <div class="settings-card-icon">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="settings-card-content">
                <h2>Invitations</h2>
                <p>Invite team members to collaborate on your recruitment process and manage user access.</p>
                <ul class="settings-features">
                    <li><i class="fas fa-check"></i> Add colleagues to your ATS</li>
                    <li><i class="fas fa-check"></i> Set user permissions</li>
                    <li><i class="fas fa-check"></i> Track invitation status</li>
                    <li><i class="fas fa-check"></i> Manage team collaboration</li>
                </ul>
                <a href="{% url 'invite_user' %}" class="settings-card-button">
                    Manage Invitations
                    <i class="fas fa-chevron-right"></i>
                </a>
            </div>
        </div>

        <!-- Job Portals Card -->
        <!-- make it "coming soon" and inactive -->
        <div class="settings-card disabled">
            <div class="settings-card-icon">
                <i class="fas fa-globe"></i>
            </div>
            <div class="settings-card-content">
                <h2>Job Portals</h2>
                <p>Configure connections to external job boards and manage API credentials for job publishing.</p>
                <ul class="settings-features">
                    <li><i class="fas fa-check"></i> Connect to major job boards</li>
                    <li><i class="fas fa-check"></i> Manage API tokens securely</li>
                    <li><i class="fas fa-check"></i> Customize portal preferences</li>
                    <li><i class="fas fa-check"></i> Track portal integration status</li>
                </ul>
                <a class="settings-card-button disabled">
                    (Coming Soon!)
                    <i class="fas fa-chevron-right"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Help & Support Section -->
    <div class="support-section">
        <div class="support-content">
            <h2>Need Help?</h2>
            <p>Our support team is ready to assist you with any questions about configuring your ATS.</p>
            <a href="mailto:<EMAIL>" class="support-button" style="text-decoration: none;">Contact Support</a>
        </div>
        <div class="support-image">
            <i class="fas fa-headset"></i>
        </div>
    </div>
</div>

<style>
    :root {
        --primary: #4a6cf7;
        --primary-hover: #3859e9;
        --secondary: #f5f8ff;
        --text-color: #333;
        --border-color: #ddd;
        --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        --success: #28a745;
        --gradient-start: #4a6cf7;
        --gradient-end: #6577F3;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
        background-color: #f9fafc;
        color: var(--text-color);
        line-height: 1.6;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    /* Settings Header */
    .settings-header {
        text-align: center;
        margin-bottom: 50px;
    }

    .settings-header h1 {
        font-size: 32px;
        color: #252b42;
        margin-bottom: 12px;
    }

    .settings-subtitle {
        font-size: 18px;
        color: #666;
    }

    /* Settings Grid */
    .settings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
        gap: 30px;
        margin-bottom: 50px;
    }

    /* Settings Card */
    .settings-card {
        background-color: white;
        border-radius: 12px;
        display: flex;
        overflow: hidden;
        box-shadow: var(--card-shadow);
        transition: transform 0.3s, box-shadow 0.3s;
        height: 100%;
    }

    .settings-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .settings-card-icon {
        flex: 0 0 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(to bottom right, var(--gradient-start), var(--gradient-end));
        color: white;
    }

    .settings-card-icon i {
        font-size: 48px;
    }

    .settings-card-content {
        flex: 1;
        padding: 30px;
        display: flex;
        flex-direction: column;
    }

    .settings-card-content h2 {
        font-size: 24px;
        color: #252b42;
        margin-bottom: 15px;
    }

    .settings-card-content p {
        color: #666;
        margin-bottom: 20px;
        font-size: 16px;
    }

    .settings-features {
        list-style-type: none;
        margin-bottom: 25px;
    }

    .settings-features li {
        padding: 6px 0;
        color: #555;
        display: flex;
        align-items: center;
    }

    .settings-features li i {
        color: var(--success);
        margin-right: 10px;
    }

    .settings-card-button {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: var(--secondary);
        color: var(--primary);
        text-decoration: none;
        padding: 12px 20px;
        border-radius: 6px;
        font-weight: 600;
        transition: background-color 0.3s;
        margin-top: auto;
    }

    .settings-card-button:hover {
        background-color: #e6f0ff;
    }

    /* Support Section */
    .support-section {
        background-color: white;
        border-radius: 12px;
        padding: 40px;
        display: flex;
        align-items: center;
        box-shadow: var(--card-shadow);
        margin-top: 50px;
    }

    .support-content {
        flex: 1;
    }

    .support-content h2 {
        font-size: 24px;
        color: #252b42;
        margin-bottom: 10px;
    }

    .support-content p {
        color: #666;
        margin-bottom: 20px;
        max-width: 500px;
    }

    .support-button {
        background-color: var(--primary);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .support-button:hover {
        background-color: var(--primary-hover);
    }

    .support-image {
        flex: 0 0 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 60px;
        color: var(--primary);
    }

    /* Media Queries */
    @media (max-width: 1024px) {
        .settings-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .settings-card {
            flex-direction: column;
        }

        .settings-card-icon {
            flex: 0 0 auto;
            height: 120px;
            width: 100%;
        }

        .support-section {
            flex-direction: column;
            text-align: center;
        }

        .support-content {
            margin-bottom: 30px;
        }

        .support-content p {
            max-width: 100%;
        }
    }

    @media (max-width: 576px) {
        .settings-card-content {
            padding: 20px;
        }

        .settings-header h1 {
            font-size: 28px;
        }

        .settings-subtitle {
            font-size: 16px;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Support button action
        document.querySelector('.support-button').addEventListener('click', function() {
            // For now, just an alert. In production, this could open a support chat window,
            // a contact form modal, or direct the user to a support page.
            alert('Support functionality will be implemented in a future update.');
        });

        // Add hover effects for cards
        const cards = document.querySelectorAll('.settings-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                const button = this.querySelector('.settings-card-button');
                button.style.backgroundColor = '#e6f0ff';
            });

            card.addEventListener('mouseleave', function() {
                const button = this.querySelector('.settings-card-button');
                button.style.backgroundColor = var(--secondary);
            });
        });
    });
</script>
{% endblock %}